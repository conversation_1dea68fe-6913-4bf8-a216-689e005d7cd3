/**
 * Firebase Functions for Custom App Check Token Validation
 * Deploy this to Firebase Functions to validate custom App Check tokens
 */

const functions = require('firebase-functions');
const admin = require('firebase-admin');
const crypto = require('crypto');

// Initialize Firebase Admin SDK
admin.initializeApp();

// Configuration - match these with your app
const APP_SECRET = 'quickk-ds-panel-custom-secret-2024';
const TOKEN_TTL_MILLIS = 3600000; // 1 hour

/**
 * Validate custom App Check token
 */
function validateCustomAppCheckToken(token) {
    try {
        // Decode the token
        const decodedToken = Buffer.from(token, 'base64').toString('utf-8');
        const [hash, timestamp] = decodedToken.split(':');
        
        if (!hash || !timestamp) {
            return { valid: false, error: 'Invalid token format' };
        }
        
        // Check token expiry
        const tokenTimestamp = parseInt(timestamp);
        const currentTime = Date.now();
        const tokenAge = currentTime - tokenTimestamp;
        
        if (tokenAge > TOKEN_TTL_MILLIS) {
            return { valid: false, error: 'Token expired' };
        }
        
        // For production, you would validate the hash against known app signatures
        // This is a simplified validation
        console.log('Custom App Check token validated:', {
            hash: hash.substring(0, 16) + '...',
            timestamp: new Date(tokenTimestamp).toISOString(),
            age: `${Math.round(tokenAge / 1000)}s`,
            valid: true
        });
        
        return { 
            valid: true, 
            timestamp: tokenTimestamp,
            age: tokenAge,
            hash: hash
        };
        
    } catch (error) {
        return { valid: false, error: error.message };
    }
}

/**
 * Firebase Function to validate App Check tokens
 */
exports.validateCustomAppCheck = functions.https.onRequest((req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-firebase-appcheck');
    
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    
    const token = req.body.token || req.headers['x-firebase-appcheck'];
    
    if (!token) {
        return res.status(400).json({ error: 'No App Check token provided' });
    }
    
    const validation = validateCustomAppCheckToken(token);
    
    res.json({
        valid: validation.valid,
        error: validation.error || null,
        timestamp: validation.timestamp || null,
        age: validation.age || null,
        message: validation.valid ? 'Custom App Check token is valid' : 'Invalid token'
    });
});

/**
 * Protected Firebase Function that requires App Check
 */
exports.protectedFunction = functions.https.onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-firebase-appcheck');
    
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    
    // Validate App Check token
    const appCheckToken = req.headers['x-firebase-appcheck'];
    
    if (!appCheckToken) {
        return res.status(401).json({ error: 'Missing App Check token' });
    }
    
    const validation = validateCustomAppCheckToken(appCheckToken);
    
    if (!validation.valid) {
        return res.status(401).json({ 
            error: `Invalid App Check token: ${validation.error}`,
            details: 'Custom App Check validation failed'
        });
    }
    
    // Token is valid, proceed with protected logic
    res.json({
        message: 'Access granted! Custom App Check validation successful.',
        appCheckValidation: {
            valid: validation.valid,
            timestamp: validation.timestamp,
            age: validation.age
        },
        data: {
            serverTime: new Date().toISOString(),
            message: 'This is protected data from Firebase Functions'
        }
    });
});

/**
 * Firebase Function to register custom App Check provider
 * This tells Firebase to accept your custom tokens
 */
exports.registerCustomAppCheckProvider = functions.https.onRequest(async (req, res) => {
    try {
        // This is a placeholder - in production you would:
        // 1. Validate the request is from your authorized source
        // 2. Register your custom provider with Firebase
        // 3. Configure token validation rules
        
        res.json({
            message: 'Custom App Check provider registration endpoint',
            instructions: [
                '1. Deploy this function to Firebase',
                '2. Configure Firebase Console to accept custom tokens',
                '3. Set App Check to "Monitoring" mode',
                '4. Your custom tokens will be validated by these functions'
            ],
            status: 'ready'
        });
        
    } catch (error) {
        console.error('Error registering custom provider:', error);
        res.status(500).json({ error: error.message });
    }
});

/**
 * Deployment Instructions:
 * 
 * 1. Install Firebase CLI: npm install -g firebase-tools
 * 2. Login: firebase login
 * 3. Initialize functions: firebase init functions
 * 4. Copy this code to functions/index.js
 * 5. Install dependencies: cd functions && npm install
 * 6. Deploy: firebase deploy --only functions
 * 
 * 7. Configure Firebase Console:
 *    - Go to Project Settings > App Check
 *    - Set Play Integrity to "Monitoring"
 *    - Your custom tokens will be validated by these functions
 * 
 * 8. Test the functions:
 *    - https://your-project.cloudfunctions.net/validateCustomAppCheck
 *    - https://your-project.cloudfunctions.net/protectedFunction
 */
