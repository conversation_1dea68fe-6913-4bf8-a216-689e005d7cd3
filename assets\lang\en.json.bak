{"app_name": "Quickk DS", "dashboard": "Dashboard", "welcome": "Welcome", "login": "<PERSON><PERSON>", "logout": "Logout", "mobile_number": "Mobile Number", "password": "Password", "forgot_password": "Forgot Password?", "login_with_otp": "Login with OTP", "notifications": "Notifications", "no_notifications": "No notifications yet", "profile": "Profile", "check_in": "Check In", "check_out": "Check Out", "create_order": "Create Order", "my_network": "My Network", "add_outlet": "Add Outlet", "clean_up": "Clean Up", "cancel": "Cancel", "submit": "Submit", "save": "Save", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "language": "Language", "english": "English", "hindi": "Hindi", "change_language": "Change Language", "settings": "Settings", "theme": "Theme", "dark_mode": "Dark Mode", "light_mode": "Light Mode", "system_default": "System Default", "about": "About", "version": "Version", "terms_conditions": "Terms & Conditions", "privacy_policy": "Privacy Policy", "contact_us": "Contact Us", "help": "Help", "faq": "FAQ", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "rate_app": "Rate App", "share_app": "Share App", "update_app": "Update App", "exit_app": "Exit App", "press_back_again_to_exit": "Press back again to exit", "location_services_disabled": "Location services are disabled.", "location_permissions_denied": "Location permissions are denied", "location_permissions_permanently_denied": "Location permissions are permanently denied, we cannot request permissions.", "kyc_pending": "KYC is pending, please contact Quickk support team", "user_not_found": "User not found. Please contact Quickk support team.", "already_logged_in": "You are already logged in on another device. Please log out from that device first.", "clean_up_notifications": "Clean Up Notifications", "clean_up_notifications_confirm": "This will remove all read notifications that are older than 7 days. Continue?", "old_notifications_cleaned_up": "Old notifications cleaned up", "test": "Test"}