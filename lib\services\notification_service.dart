import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import './user_data_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Timer? _cleanupTimer;
  int _unreadCount = 0;
  final _unreadCountController = StreamController<int>.broadcast();

  Stream<int> get unreadCountStream => _unreadCountController.stream;
  int get unreadCount => _unreadCount;

  // Initialize notification service
  Future<void> init(GlobalKey<NavigatorState> navigatorKey) async {
    try {
      tz_data.initializeTimeZones();

      // Initialize local notifications
      const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');
      final DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) async {
          // Handle iOS foreground notification
        },
      );
      final InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin with error handling
      final bool? initialized = await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          // Handle notification tap
          if (response.payload != null) {
            navigatorKey.currentState?.pushNamed('/notifications');
          }
        },
      );

      print('Notification plugin initialized: $initialized');

      // Request permission
      await _requestPermissions();

      // Start cleanup timer
      await cleanupOldNotifications();
      _startCleanupTimer();

      // Start listening to unread notifications
      _listenToUnreadNotifications();
    } catch (e) {
      print('Error initializing notification service: $e');
    }
  }

  Future<void> _requestPermissions() async {
    // For Android 13 and above
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();

    // For iOS
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  // Show a local notification
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    bool fromFCM = false,
    String? messageId,
  }) async {
    try {
      // Define notification details
      const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'ds_panel_channel',
        'DS Panel Notifications',
        channelDescription: 'Notifications for DS Panel app',
        importance: Importance.max,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Generate a unique ID for the notification
      final int notificationId = DateTime.now().millisecondsSinceEpoch.remainder(100000);

      print('Showing notification with ID: $notificationId');
      await flutterLocalNotificationsPlugin.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );
      print('Notification shown successfully');

      // Save notification to Firestore if it's not from FCM or if it's from FCM but we need to save it
      if (!fromFCM) {
        await saveNotificationToFirestore(title, body, payload);
      } else if (fromFCM && messageId != null) {
        // For FCM notifications, check if it already exists before saving
        await saveNotificationFromFCM(title, body, payload, messageId);
      }

      // Force update the unread count
      await _updateUnreadCount();
    } catch (e) {
      print('Error showing notification: $e');
    }
  }

  // Schedule a notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'ds_panel_channel',
        'DS Panel Notifications',
        channelDescription: 'Notifications for DS Panel app',
        importance: Importance.max,
        priority: Priority.high,
      );
      const NotificationDetails platformChannelSpecifics = NotificationDetails(android: androidPlatformChannelSpecifics);

      // Generate a unique ID for the notification
      final int notificationId = DateTime.now().millisecondsSinceEpoch.remainder(100000);

      print('Scheduling notification with ID: $notificationId for ${scheduledDate.toString()}');
      await flutterLocalNotificationsPlugin.zonedSchedule(
        notificationId,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        platformChannelSpecifics,
        androidAllowWhileIdle: true,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );
      print('Notification scheduled successfully');

      // Save notification to Firestore
      await saveNotificationToFirestore(title, body, payload, scheduledDate: scheduledDate);
    } catch (e) {
      print('Error scheduling notification: $e');
    }
  }

  // Save notification to Firestore
  Future<void> saveNotificationToFirestore(
    String title,
    String body,
    String? payload, {
    DateTime? scheduledDate,
  }) async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        print('Saving notification to Firestore for user: $userDocId');
        await _firestore.collection('ds_notifications').add({
          'userId': userDocId,
          'title': title,
          'body': body,
          'payload': payload,
          'isRead': false,
          'createdAt': scheduledDate ?? FieldValue.serverTimestamp(),
          'scheduledFor': scheduledDate,
        });

        // Force update the unread count
        await _updateUnreadCount();
        print('Notification saved to Firestore and unread count updated');
      } else {
        print('Cannot save notification: No user logged in');
      }
    } catch (e) {
      print('Error saving notification to Firestore: $e');
    }
  }

  // Save notification from FCM to Firestore with duplicate checking
  Future<void> saveNotificationFromFCM(
    String title,
    String body,
    String? payload,
    String messageId,
  ) async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Check if this notification has already been processed
        QuerySnapshot existingNotifications = await _firestore
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .where('fcmMessageId', isEqualTo: messageId)
            .limit(1)
            .get();

        if (existingNotifications.docs.isEmpty) {
          print('Saving FCM notification to Firestore for user: $userDocId');
          await _firestore.collection('ds_notifications').add({
            'userId': userDocId,
            'title': title,
            'body': body,
            'payload': payload,
            'isRead': false,
            'createdAt': FieldValue.serverTimestamp(),
            'fcmMessageId': messageId, // Store the message ID to prevent duplicates
          });
          print('FCM notification saved to Firestore');
        } else {
          print('Duplicate FCM notification detected and skipped: $messageId');
        }

        // Force update the unread count
        await _updateUnreadCount();
      } else {
        print('Cannot save FCM notification: No user logged in');
      }
    } catch (e) {
      print('Error saving FCM notification to Firestore: $e');
    }
  }

  // Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore.collection('ds_notifications').doc(notificationId).update({
        'isRead': true,
      });
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllNotificationsAsRead() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        QuerySnapshot querySnapshot = await _firestore
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .where('isRead', isEqualTo: false)
            .get();

        WriteBatch batch = _firestore.batch();
        for (var doc in querySnapshot.docs) {
          batch.update(doc.reference, {'isRead': true});
        }
        await batch.commit();
      }
    } catch (e) {
      print('Error marking all notifications as read: $e');
    }
  }

  // Clean up old notifications (older than 7 days and read)
  Future<void> cleanupOldNotifications() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Calculate date 7 days ago
        DateTime sevenDaysAgo = DateTime.now().subtract(Duration(days: 7));

        // Get read notifications older than 7 days
        QuerySnapshot querySnapshot = await _firestore
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .where('isRead', isEqualTo: true)
            .get();

        WriteBatch batch = _firestore.batch();
        int deletedCount = 0;

        for (var doc in querySnapshot.docs) {
          // Check if the notification is older than 7 days
          Timestamp timestamp = doc['createdAt'] as Timestamp;
          if (timestamp.toDate().isBefore(sevenDaysAgo)) {
            batch.delete(doc.reference);
            deletedCount++;
          }
        }

        if (deletedCount > 0) {
          await batch.commit();
          print('Cleaned up $deletedCount old notifications');
        }
      }
    } catch (e) {
      print('Error cleaning up old notifications: $e');
    }
  }

  // Start cleanup timer (runs once a day)
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(Duration(days: 1), (timer) {
      cleanupOldNotifications();
    });
  }

  // Listen to unread notifications count
  Future<void> _listenToUnreadNotifications() async {
    // Get the user document ID from our service
    String? userDocId = await UserDataService().getUserDocumentId();

    if (userDocId != null) {
      print('Setting up notification listener for user: $userDocId');

      // We've moved the welcome notification creation to the dashboard screen
      // to ensure it's created after the user is fully authenticated

      // Set up a listener for unread notifications
      _firestore
          .collection('ds_notifications')
          .where('userId', isEqualTo: userDocId)
          .where('isRead', isEqualTo: false)
          .snapshots()
          .listen((snapshot) {
        _unreadCount = snapshot.docs.length;
        print('Unread notifications count from listener: $_unreadCount');
        _unreadCountController.add(_unreadCount);
      }, onError: (error) {
        print('Error listening to notifications: $error');
      });

      // Immediately fetch the current count
      await _updateUnreadCount();

      // Debug: Print all notifications for this user
      try {
        QuerySnapshot allNotifications = await _firestore
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .get();

        print('DEBUG: Found ${allNotifications.docs.length} total notifications for user: $userDocId');

        for (var doc in allNotifications.docs) {
          var data = doc.data() as Map<String, dynamic>;
          print('DEBUG: Notification - Title: ${data['title']}, Read: ${data['isRead']}');
        }
      } catch (e) {
        print('DEBUG: Error fetching all notifications: $e');
      }
    }
  }

  // Update unread count immediately (private implementation)
  Future<void> _updateUnreadCount() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        print('Updating unread count for user: $userDocId');
        QuerySnapshot snapshot = await _firestore
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .where('isRead', isEqualTo: false)
            .get();

        int previousCount = _unreadCount;
        _unreadCount = snapshot.docs.length;
        print('Unread count updated: $previousCount -> $_unreadCount');

        // Only notify listeners if the count has changed
        if (previousCount != _unreadCount) {
          _unreadCountController.add(_unreadCount);
          print('Notified listeners of new unread count: $_unreadCount');
        }
      } else {
        print('Cannot update unread count: No user logged in');
      }
    } catch (e) {
      print('Error updating unread count: $e');
    }
  }

  // Public method to update unread count
  Future<void> updateUnreadCount() async {
    return _updateUnreadCount();
  }

  // Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _unreadCountController.close();
  }
}
