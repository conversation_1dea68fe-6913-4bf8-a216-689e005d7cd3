/**
 * Firebase Functions for DS Panel Custom App Check
 * Deploy this to Firebase Functions
 */

const {onRequest} = require('firebase-functions/v2/https');
const {setGlobalOptions} = require('firebase-functions/v2');
const admin = require('firebase-admin');
const crypto = require('crypto');

// Set global options
setGlobalOptions({maxInstances: 10});

// Initialize Firebase Admin SDK
admin.initializeApp();

// Configuration
const APP_ID = '1:665703047524:android:9966893b14dfa2fc6fda71';
const APP_SECRET = 'quickk-ds-panel-custom-secret-2024';
const TOKEN_TTL_SECONDS = 3600;
const MAX_TOKENS_PER_DEVICE = 10;
const RATE_LIMIT_WINDOW = 3600000; // 1 hour in milliseconds

// Firestore collections
const DEVICES_COLLECTION = 'app_check_devices';
const RATE_LIMIT_COLLECTION = 'app_check_rate_limits';

/**
 * Validate and track device information
 */
async function validateAndTrackDevice(deviceInfo) {
    try {
        const { androidId, manufacturer, model, fingerprint, sdkInt } = deviceInfo;
        
        if (!androidId || !manufacturer || !model || !fingerprint) {
            return { valid: false, error: 'Invalid device info' };
        }

        // Minimum Android SDK requirement (Android 6.0)
        if (sdkInt < 23) {
            return { valid: false, error: 'Unsupported Android version' };
        }

        const deviceRef = admin.firestore().collection(DEVICES_COLLECTION).doc(androidId);
        const deviceDoc = await deviceRef.get();

        if (!deviceDoc.exists) {
            // New device - store it
            await deviceRef.set({
                manufacturer,
                model,
                fingerprint,
                sdkInt,
                firstSeen: admin.firestore.FieldValue.serverTimestamp(),
                lastSeen: admin.firestore.FieldValue.serverTimestamp(),
                tokenCount: 0
            });
        } else {
            // Update existing device
            await deviceRef.update({
                lastSeen: admin.firestore.FieldValue.serverTimestamp(),
                tokenCount: admin.firestore.FieldValue.increment(1)
            });

            // Check token count
            const deviceData = deviceDoc.data();
            if (deviceData.tokenCount >= MAX_TOKENS_PER_DEVICE) {
                return { valid: false, error: 'Token limit exceeded' };
            }
        }

        return { valid: true };
    } catch (error) {
        console.error('Device validation error:', error);
        return { valid: false, error: 'Device validation failed' };
    }
}

/**
 * Check rate limiting for a device
 */
async function checkRateLimit(deviceId) {
    const rateLimitRef = admin.firestore().collection(RATE_LIMIT_COLLECTION).doc(deviceId);
    const now = Date.now();

    try {
        const result = await admin.firestore().runTransaction(async (transaction) => {
            const doc = await transaction.get(rateLimitRef);
            
            if (!doc.exists) {
                transaction.set(rateLimitRef, {
                    count: 1,
                    windowStart: now
                });
                return { allowed: true };
            }

            const data = doc.data();
            const windowAge = now - data.windowStart;

            if (windowAge > RATE_LIMIT_WINDOW) {
                // Reset window
                transaction.set(rateLimitRef, {
                    count: 1,
                    windowStart: now
                });
                return { allowed: true };
            }

            if (data.count >= MAX_TOKENS_PER_DEVICE) {
                return {
                    allowed: false,
                    retryAfter: Math.ceil((RATE_LIMIT_WINDOW - windowAge) / 1000)
                };
            }

            transaction.update(rateLimitRef, {
                count: admin.firestore.FieldValue.increment(1)
            });
            return { allowed: true };
        });

        return result;
    } catch (error) {
        console.error('Rate limit check failed:', error);
        return { allowed: false, error: 'Rate limit check failed' };
    }
}

/**
 * Generate App Check token
 */
exports.generateAppCheckToken = onRequest({
    timeoutSeconds: 60,
    memory: '256MiB'
}, async (req, res) => {
    console.log('Function started at:', new Date().toISOString());

    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }

    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }

    try {
        const { deviceInfo, appSignature, nonce } = req.body;
        const deviceSecret = req.headers['x-app-secret'];

        if (!deviceInfo || !appSignature || !deviceSecret) {
            return res.status(400).json({ error: 'Missing required fields' });
        }

        // Validate device
        const deviceValidation = await validateAndTrackDevice(deviceInfo);
        if (!deviceValidation.valid) {
            return res.status(403).json({ error: deviceValidation.error });
        }

        // Check rate limit
        const rateLimit = await checkRateLimit(deviceInfo.androidId);
        if (!rateLimit.allowed) {
            return res.status(429).json({
                error: 'Rate limit exceeded',
                retryAfter: rateLimit.retryAfter
            });
        }

        // Generate App Check token
        const appCheckToken = await admin.appCheck().createToken(APP_ID, {
            ttlMillis: TOKEN_TTL_SECONDS * 1000,
            customClaims: {
                deviceId: deviceInfo.androidId,
                appSignature: appSignature
            }
        });

        return res.status(200).json({
            token: appCheckToken.token,
            ttlSeconds: TOKEN_TTL_SECONDS
        });

    } catch (error) {
        console.error('Token generation error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Firebase Function: Validate App Check Token
 * URL: https://your-project-id.cloudfunctions.net/validateAppCheckToken
 */
exports.validateAppCheckToken = onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }

    try {
        const { token } = req.body;

        if (!token) {
            return res.status(400).json({ error: 'Missing token' });
        }

        // Verify App Check token
        const decodedToken = await admin.appCheck().verifyToken(token);

        res.json({
            valid: true,
            appId: decodedToken.app_id,
            customClaims: decodedToken.custom_claims || {},
            message: 'App Check token is valid'
        });

    } catch (error) {
        console.error('Error validating App Check token:', error);
        res.status(401).json({
            valid: false,
            error: 'Invalid App Check token',
            details: error.message
        });
    }
});

/**
 * Health check function
 */
exports.healthCheck = onRequest((req, res) => {
    res.json({
        status: 'healthy',
        service: 'DS Panel Custom App Check Service',
        timestamp: new Date().toISOString(),
        functions: [
            'generateAppCheckToken',
            'validateAppCheckToken',
            'healthCheck'
        ]
    });
});

/**
 * Firebase Function: Generate App Check Token (Callable)
 * URL: https://your-project-id.cloudfunctions.net/generateAppCheckTokenCallable
 */
exports.generateAppCheckTokenCallable = functions.https.onCall(async (data, context) => {
    try {
        const { deviceInfo, nonce } = data;
        
        // Validate the request
        if (!deviceInfo || !nonce) {
            throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
        }

        // Create a device fingerprint hash
        const deviceFingerprint = crypto
            .createHash('sha256')
            .update(JSON.stringify(deviceInfo))
            .digest('hex');

        // Check if this is a known device (you should implement your own validation logic)
        const isKnownDevice = await validateDevice(deviceInfo);
        
        if (!isKnownDevice) {
            throw new functions.https.HttpsError('permission-denied', 'Unknown or invalid device');
        }

        // Generate a custom App Check token
        const token = await admin.appCheck().createToken(process.env.FIREBASE_APP_ID, {
            ttlMillis: 3600000, // 1 hour
        });

        return { token: token.token };
    } catch (error) {
        console.error('Error generating App Check token:', error);
        throw new functions.https.HttpsError('internal', 'Failed to generate token');
    }
});

async function validateDevice(deviceInfo) {
    // Implement your device validation logic here
    // For example, check against a whitelist of known devices
    // or validate using your own criteria
    
    // This is a basic example - replace with your actual validation logic
    const {
        manufacturer,
        model,
        androidId,
        fingerprint,
        securityPatch,
        sdkInt
    } = deviceInfo;

    // Basic validation - ensure all required fields are present
    if (!manufacturer || !model || !androidId || !fingerprint) {
        return false;
    }

    // Check minimum SDK version (example: Android 6.0 minimum)
    if (sdkInt < 23) {
        return false;
    }

    // You should add more sophisticated validation here
    // For example:
    // - Check against a database of known devices
    // - Validate the device fingerprint
    // - Check for rooted devices
    // - Implement rate limiting
    // - etc.

    return true;
}

/**
 * Deployment Instructions:
 *
 * 1. Install Firebase CLI: npm install -g firebase-tools
 * 2. Login: firebase login
 * 3. Go to your project directory
 * 4. Initialize functions: firebase init functions
 * 5. Replace functions/index.js with this code
 * 6. Deploy: firebase deploy --only functions
 *
 * Your functions will be available at:
 * https://YOUR-PROJECT-ID.cloudfunctions.net/generateAppCheckToken
 * https://YOUR-PROJECT-ID.cloudfunctions.net/validateAppCheckToken
 * https://YOUR-PROJECT-ID.cloudfunctions.net/healthCheck
 */