import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../theme/app_theme.dart';
import '../services/fcm_service.dart';

import '../utils/localization.dart';
import '../services/user_data_service.dart';
import 'otp_verification_screen.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _mobileNumberController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? _deviceId;
  bool _isOtpDialogShown = false;  // Flag to track if OTP dialog is already shown

  // Add cooldown tracking for SMS verification
  DateTime? _lastSmsRequestTime;
  static const int _smsCooldownSeconds = 60; // 1 minute cooldown

  @override
  void initState() {
    super.initState();
    // Add listener to mobile number controller to update UI when text changes
    _mobileNumberController.addListener(() {
      setState(() {
        // This empty setState will trigger a rebuild to update the button state
      });
    });
  }

  @override
  void dispose() {
    // Remove listener when the widget is disposed
    _mobileNumberController.removeListener(() {});
    _mobileNumberController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _getDeviceId(); // Safe to call here
  }

  Future<void> _getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Theme.of(context).platform == TargetPlatform.android) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        _deviceId = androidInfo.id; // Use 'id' instead of 'androidId'
      });
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      setState(() {
        _deviceId = iosInfo.identifierForVendor; // Unique ID on iOS
      });
    }
  }

  // This method has been removed as we're now using a separate OTP verification screen

  // State variable to track if login is in progress
  bool _isLoggingIn = false;

  // Login method - directly initiates OTP verification
  void _login() async {
    // Prevent multiple button presses
    if (_isLoggingIn) return;

    // Set loading state
    setState(() {
      _isLoggingIn = true;
    });

    final mobileNumber = _mobileNumberController.text.trim();

    if (_formKey.currentState!.validate()) {
      // Check if we're in the cooldown period
      if (_lastSmsRequestTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_lastSmsRequestTime!);
        if (difference.inSeconds < _smsCooldownSeconds) {
          final remainingSeconds = _smsCooldownSeconds - difference.inSeconds;
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Please wait ${remainingSeconds} seconds before requesting another OTP.'),
          ));
          // Reset loading state
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }
      }

      // Check if OTP dialog is already shown
      if (_isOtpDialogShown) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Verification already in progress. Please wait.'),
        ));
        // Reset loading state
        setState(() {
          _isLoggingIn = false;
        });
        return;
      }

      try {
        // First, check if the user exists in Firestore by mobile number
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('users')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        if (userQuery.docs.isEmpty) {
          // User doesn't exist in Firestore
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User not found. Please contact Quickk support team.'),
          ));
          // Reset loading state
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // User exists, check KYC status first
        // Get the user document data
        DocumentSnapshot userDoc = userQuery.docs.first;
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

        // Check KYC status
        String kycStatus = userData['kyc'] ?? 'pending';

        if (kycStatus != 'approved') {
          // KYC not approved, show message and don't proceed with OTP
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('KYC is pending. Please contact Quickk support team.'),
            duration: Duration(seconds: 3),
          ));
          // Reset loading state
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // KYC is approved, proceed with OTP verification
        // Update the last SMS request time
        _lastSmsRequestTime = DateTime.now();
        _isOtpDialogShown = true;

        // Show a loading indicator
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Sending verification code...'),
          duration: Duration(seconds: 2),
        ));

        // Debug Google Play Services and Firebase status
        print('Firebase App Check is disabled - proceeding with authentication');
        print('Device ID: $_deviceId');
        print('Mobile Number: $mobileNumber');
        print('Attempting Firebase Auth with phone number: +91$mobileNumber');

        // Initiate phone verification
        try {
          await FirebaseAuth.instance.verifyPhoneNumber(
            phoneNumber: '+91$mobileNumber',
            verificationCompleted: (PhoneAuthCredential credential) async {
              // Disable auto-verification - force manual OTP entry
              print('🚫 Auto-verification disabled - user must enter OTP manually');
              print('Detected SMS code: ${credential.smsCode} (ignored)');
              // Do NOT sign in automatically - user must enter OTP manually
            },
            verificationFailed: (FirebaseAuthException e) {
              _isOtpDialogShown = false;
              print('Verification failed: ${e.message}');

              // Reset loading state
              setState(() {
                _isLoggingIn = false;
              });

              String errorMessage = 'Verification failed';

              // Handle specific error codes
              if (e.code == 'too-many-requests' || e.message?.contains('unusual activity') == true) {
                errorMessage = 'Too many requests. Please try again after some time.';
              } else if (e.code == 'invalid-phone-number') {
                errorMessage = 'Invalid phone number format. Please enter a valid number.';
              } else if (e.message != null) {
                errorMessage = 'Verification failed: ${e.message}';
              }

              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text(errorMessage),
              ));
            },
            codeSent: (String verificationId, int? resendToken) {
              print('Verification code sent to $mobileNumber');
              _isOtpDialogShown = false;

              // Reset loading state
              setState(() {
                _isLoggingIn = false;
              });

              // Navigate to OTP verification screen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => OtpVerificationScreen(
                    mobileNumber: mobileNumber,
                    verificationId: verificationId,
                    userData: userData,
                  ),
                ),
              );
            },
            codeAutoRetrievalTimeout: (String verificationId) {
              _isOtpDialogShown = false;
              print('⏰ Auto-retrieval timeout (disabled by design)');
              print('User must enter OTP manually as intended');
            },
            timeout: Duration(seconds: 60), // Standard timeout - auto-retrieval disabled
          );
        } catch (e) {
          _isOtpDialogShown = false;
          print('Error initiating phone verification: $e');

          // Reset loading state
          setState(() {
            _isLoggingIn = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Error sending verification code: $e'),
          ));
        }
      } catch (e) {
        print('Error during login: $e');

        // Reset loading state
        setState(() {
          _isLoggingIn = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Error during login: $e'),
        ));
      }
    }
  }

  // We've removed the OTP dialog method as we're now using a separate screen

  // Sign in with phone credential
  Future<void> _signInWithPhoneCredential(PhoneAuthCredential credential, Map<String, dynamic> userData) async {
    try {
      print('Attempting to sign in with phone credential');
      // Sign in with the credential
      UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Get the user ID
      String userId = userCredential.user!.uid;
      print('Successfully signed in with phone credential. User ID: $userId');

      // Check if the user document exists with this UID
      print('Fetching user document from Firestore');
      DocumentSnapshot userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      print('User document exists: ${userDoc.exists}');
      if (userDoc.exists) {
        print('User document data: ${userDoc.data()}');
      }

      // Instead of creating a new document, we'll find the existing document by mobile number
      // and update it with the Firebase Auth UID
      String mobileNumber = userData['mobileNumber'];

      // Check if there's an existing document with this mobile number
      QuerySnapshot existingUserQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('mobileNumber', isEqualTo: mobileNumber)
          .get();

      if (existingUserQuery.docs.isNotEmpty) {
        // Get the existing document
        DocumentSnapshot existingUserDoc = existingUserQuery.docs.first;
        String existingDocId = existingUserDoc.id;

        // If the existing document has a different ID than the Firebase Auth UID
        if (existingDocId != userId) {
          print('Found existing user document with ID: $existingDocId');

          // Get the data from the existing document
          Map<String, dynamic> existingData = existingUserDoc.data() as Map<String, dynamic>;

          // Update the authStatus field
          existingData['authStatus'] = 'done';
          existingData['firebaseAuthUid'] = userId; // Store the Firebase Auth UID for reference

          // Update the existing document
          await FirebaseFirestore.instance
              .collection('users')
              .doc(existingDocId)
              .update(existingData);

          print('Updated existing user document with Firebase Auth UID');

          // Now we need to use the existing document ID for all operations
          userId = existingDocId;

          // Store the document ID in our service for future use
          await UserDataService().setUserDocumentId(userId);

          // Fetch the updated document
          userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();
        } else {
          // The document already exists with the Firebase Auth UID, just update authStatus
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({'authStatus': 'done'});
        }
      } else if (!userDoc.exists) {
        // This is a fallback case - if somehow we can't find the document by mobile number
        // but we also don't have a document with the Firebase Auth UID
        print('Warning: Could not find user document by mobile number or Firebase Auth UID');

        // Create a new document with the Auth UID as a last resort
        userData['authStatus'] = 'done';

        await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .set(userData);

        print('Created new user document with Firebase Auth UID as fallback');
      }

      // Get the latest user data
      userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      // Safely access fields with null checks
      Map<String, dynamic>? data = userDoc.data() as Map<String, dynamic>?;
      String? currentDeviceId = data?['deviceId'];

      // We've already checked KYC status before sending OTP, so we know it's approved
      // Just check device ID
      if (currentDeviceId == null || currentDeviceId == _deviceId) {
        // Update Firestore with the current device ID
        try {
          print('Updating device ID in Firestore to: $_deviceId');
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({'deviceId': _deviceId});
          print('Successfully updated device ID in Firestore');
        } catch (updateError) {
          print('Error updating device ID: $updateError');
          // Continue anyway, this is not critical for login
        }

        // Register FCM token for the user
        try {
          await FCMService().handleUserLogin(userCredential.user!);
          print('FCM token registered for user: $userId');
        } catch (fcmError) {
          print('Error registering FCM token: $fcmError');
          // Continue anyway, this is not critical
        }

        // Navigate to dashboard
        Navigator.pushReplacementNamed(context, '/dashboard');
      } else {
        // User is already logged in on another device
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('You are already logged in on another device. Please log out from that device first.'),
        ));
      }
    } catch (e) {
      print('Error signing in with phone credential: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error signing in: $e'),
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: 40),
                // App Logo/Icon
                Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.delivery_dining,
                      size: 60,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 24),
                // Welcome Text
                Center(
                  child: Text(
                    context.translate('welcome'),
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 8),
                Center(
                  child: Text(
                    'Log in to your Salesman account',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 40),
                // Login Form Card
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Mobile Number Field
                          TextFormField(
                            controller: _mobileNumberController,
                            keyboardType: TextInputType.phone,
                            style: TextStyle(fontSize: 16),
                            decoration: InputDecoration(
                              labelText: context.translate('mobile_number'),
                              hintText: context.translate('enter_mobile_number'),
                              prefixIcon: Icon(Icons.phone_android, color: AppTheme.primaryColor),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return context.translate('enter_mobile_number_error');
                              }
                              if (value.length != 10) {
                                return context.translate('mobile_number_length_error');
                              }
                              if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
                                return context.translate('mobile_number_digits_error');
                              }
                              return null;
                            },
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(10),
                            ],
                          ),
                          SizedBox(height: 30),
                          // Login Button
                          ElevatedButton(
                            onPressed: _isLoggingIn || _mobileNumberController.text.trim().length != 10 ? null : _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isLoggingIn || _mobileNumberController.text.trim().length != 10 ? Colors.grey : AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: _isLoggingIn
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        'Processing...',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          letterSpacing: 1.2,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    context.translate('login').toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 1.2,
                                    ),
                                  ),
                          ),
                          SizedBox(height: 20),
                          // Help text
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Text(
                                'Enter your mobile number and click Login to receive a verification code.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: AppTheme.textSecondaryColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                // First-time user note removed
              ],
            ),
          ),
        ),
      ),
    );
  }
}
