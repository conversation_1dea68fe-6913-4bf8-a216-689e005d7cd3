import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../theme/app_theme.dart';
import '../services/fcm_service.dart';

import '../utils/localization.dart';
import '../services/user_data_service.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _mobileNumberController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String? _deviceId;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    // Add listeners to controllers to update UI when text changes
    _mobileNumberController.addListener(() {
      setState(() {
        // This empty setState will trigger a rebuild to update the button state
      });
    });
    _passwordController.addListener(() {
      setState(() {
        // This empty setState will trigger a rebuild to update the button state
      });
    });
  }

  @override
  void dispose() {
    // Remove listeners when the widget is disposed
    _mobileNumberController.removeListener(() {});
    _passwordController.removeListener(() {});
    _mobileNumberController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _getDeviceId(); // Safe to call here
  }

  Future<void> _getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Theme.of(context).platform == TargetPlatform.android) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        _deviceId = androidInfo.id; // Use 'id' instead of 'androidId'
      });
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      setState(() {
        _deviceId = iosInfo.identifierForVendor; // Unique ID on iOS
      });
    }
  }

  // This method has been removed as we're now using a separate OTP verification screen

  // State variable to track if login is in progress
  bool _isLoggingIn = false;

  // Email/Password login method
  void _login() async {
    // Prevent multiple button presses
    if (_isLoggingIn) return;

    // Set loading state
    setState(() {
      _isLoggingIn = true;
    });

    final mobileNumber = _mobileNumberController.text.trim();
    final password = _passwordController.text.trim();

    // Auto-generate email from mobile number
    final email = '$<EMAIL>';

    if (_formKey.currentState!.validate()) {
      try {
        // First, check if the user exists in Firestore by mobile number
        // We'll check both users and wdUsers collections
        QuerySnapshot userQuery = await FirebaseFirestore.instance
            .collection('users')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        QuerySnapshot wdUserQuery = await FirebaseFirestore.instance
            .collection('wdUsers')
            .where('mobileNumber', isEqualTo: mobileNumber)
            .get();

        Map<String, dynamic>? userData;

        if (userQuery.docs.isNotEmpty) {
          // User found in users collection
          DocumentSnapshot userDoc = userQuery.docs.first;
          userData = userDoc.data() as Map<String, dynamic>;
        } else if (wdUserQuery.docs.isNotEmpty) {
          // User found in wdUsers collection
          DocumentSnapshot wdUserDoc = wdUserQuery.docs.first;
          userData = wdUserDoc.data() as Map<String, dynamic>;
        }

        if (userData == null) {
          // User doesn't exist in either collection
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('User not found. Please contact Quickk support team.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        // Check KYC status before proceeding
        String kycStatus = userData['kyc'] ?? '';
        if (kycStatus.toLowerCase() != 'approved') {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Your KYC is not approved. Please contact Quickk support team.'),
          ));
          setState(() {
            _isLoggingIn = false;
          });
          return;
        }

        print('User found in Firestore with KYC approved. Proceeding with email/password authentication.');

        // Try to sign in with email/password
        UserCredential? userCredential;
        try {
          userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
            email: email,
            password: password,
          );
        } catch (authError) {
          // If sign in fails, it might be the first time login
          // Create Firebase Auth account with email/password
          try {
            userCredential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
              email: email,
              password: password,
            );
            print('Created new Firebase Auth account for first-time login');
          } catch (createError) {
            print('Error creating Firebase Auth account: $createError');
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Invalid email or password. Please try again.'),
            ));
            setState(() {
              _isLoggingIn = false;
            });
            return;
          }
        }

        if (userCredential != null) {
          // Successfully authenticated, now link to existing Firestore document
          await _linkUserToFirestore(userCredential, userData, mobileNumber, password);
        }
      } catch (e) {
        print('Error during login: $e');
        setState(() {
          _isLoggingIn = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Error during login. Please try again.'),
        ));
      }
    } else {
      // Reset loading state if form validation fails
      setState(() {
        _isLoggingIn = false;
      });
    }
  }

  // Link user to existing Firestore document
  Future<void> _linkUserToFirestore(UserCredential userCredential, Map<String, dynamic> userData, String mobileNumber, String password) async {
    try {
      String userId = userCredential.user!.uid;
      print('Successfully authenticated. User ID: $userId');

      // Check if there's an existing document with this mobile number
      QuerySnapshot existingUserQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('mobileNumber', isEqualTo: mobileNumber)
          .get();

      if (existingUserQuery.docs.isNotEmpty) {
        // Get the existing document
        DocumentSnapshot existingUserDoc = existingUserQuery.docs.first;
        String existingDocId = existingUserDoc.id;

        // If the existing document has a different ID than the Firebase Auth UID
        if (existingDocId != userId) {
          print('Found existing user document with ID: $existingDocId');

          // Get the data from the existing document
          Map<String, dynamic> existingData = existingUserDoc.data() as Map<String, dynamic>;

          // Update the authStatus field, store Firebase Auth UID, and password
          existingData['authStatus'] = 'done';
          existingData['firebaseAuthUid'] = userId;
          existingData['password'] = password;

          // Update the existing document
          await FirebaseFirestore.instance
              .collection('users')
              .doc(existingDocId)
              .update(existingData);

          print('Updated existing user document with Firebase Auth UID');

          // Store the document ID in our service for future use
          await UserDataService().setUserDocumentId(existingDocId);

          // Use the existing document ID for all operations
          userId = existingDocId;
        } else {
          // The document already exists with the Firebase Auth UID, just update authStatus and password
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({
                'authStatus': 'done',
                'password': password,
              });
        }
      } else {
        // Create a new document with the Auth UID
        userData['authStatus'] = 'done';
        userData['firebaseAuthUid'] = userId;
        userData['password'] = password;

        await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .set(userData);

        print('Created new user document with Firebase Auth UID');
      }

      // Update device ID
      try {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .update({'deviceId': _deviceId});
        print('Successfully updated device ID in Firestore');
      } catch (updateError) {
        print('Error updating device ID: $updateError');
      }

      // Register FCM token for the user
      try {
        await FCMService().handleUserLogin(userCredential.user!);
        print('FCM token registered for user: $userId');
      } catch (fcmError) {
        print('Error registering FCM token: $fcmError');
      }

      // Reset loading state
      setState(() {
        _isLoggingIn = false;
      });

      // Navigate to dashboard
      Navigator.pushReplacementNamed(context, '/dashboard');

    } catch (e) {
      print('Error linking user to Firestore: $e');
      setState(() {
        _isLoggingIn = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error completing login. Please try again.'),
      ));
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: 40),
                // App Logo/Icon
                Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.delivery_dining,
                      size: 60,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 24),
                // Welcome Text
                Center(
                  child: Text(
                    context.translate('welcome'),
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 8),
                Center(
                  child: Text(
                    'Log in to your Salesman account',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
                SizedBox(height: 40),
                // Login Form Card
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Mobile Number Field
                          TextFormField(
                            controller: _mobileNumberController,
                            keyboardType: TextInputType.phone,
                            style: TextStyle(fontSize: 16),
                            decoration: InputDecoration(
                              labelText: context.translate('mobile_number'),
                              hintText: context.translate('enter_mobile_number'),
                              prefixIcon: Icon(Icons.phone_android, color: AppTheme.primaryColor),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return context.translate('enter_mobile_number_error');
                              }
                              if (value.length != 10) {
                                return context.translate('mobile_number_length_error');
                              }
                              if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
                                return context.translate('mobile_number_digits_error');
                              }
                              return null;
                            },
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(10),
                            ],
                          ),
                          SizedBox(height: 20),
                          // Password Field
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            style: TextStyle(fontSize: 16),
                            decoration: InputDecoration(
                              labelText: 'Password',
                              hintText: 'Enter your password',
                              prefixIcon: Icon(Icons.lock, color: AppTheme.primaryColor),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                                  color: AppTheme.primaryColor,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your password';
                              }
                              if (value.length < 6) {
                                return 'Password must be at least 6 characters';
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 30),
                          // Login Button
                          ElevatedButton(
                            onPressed: _isLoggingIn || _mobileNumberController.text.trim().length != 10 || _passwordController.text.trim().isEmpty ? null : _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isLoggingIn || _mobileNumberController.text.trim().length != 10 || _passwordController.text.trim().isEmpty ? Colors.grey : AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: _isLoggingIn
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        'Processing...',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          letterSpacing: 1.2,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    context.translate('login').toUpperCase(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 1.2,
                                    ),
                                  ),
                          ),
                          SizedBox(height: 20),
                          // Help text
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Text(
                                'Enter your mobile number and password to log in to your account.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: AppTheme.textSecondaryColor,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                // First-time user note removed
              ],
            ),
          ),
        ),
      ),
    );
  }
}
