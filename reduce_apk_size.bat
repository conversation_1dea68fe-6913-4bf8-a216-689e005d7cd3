@echo off
echo Reducing APK size...

REM Create a flutter_launcher_icons-minimal.yaml file
echo Creating minimal icon configuration...
echo flutter_icons: > flutter_launcher_icons-minimal.yaml
echo   android: true >> flutter_launcher_icons-minimal.yaml
echo   ios: true >> flutter_launcher_icons-minimal.yaml
echo   image_path: "assets/icon/icon.png" >> flutter_launcher_icons-minimal.yaml
echo   adaptive_icon_background: "#FFFFFF" >> flutter_launcher_icons-minimal.yaml
echo   adaptive_icon_foreground: "assets/icon/icon_foreground.png" >> flutter_launcher_icons-minimal.yaml

REM Run flutter_launcher_icons with minimal configuration
echo Generating optimized icons...
flutter pub run flutter_launcher_icons:main -f flutter_launcher_icons-minimal.yaml

echo Size reduction completed!
echo Now run build_minimal.bat to build a smaller APK.
