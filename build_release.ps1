# Build script for DS Panel with App Check
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting DS Panel build process..." -ForegroundColor Cyan

try {
    # Step 1: Clean the project
    Write-Host "🧹 Cleaning project..." -ForegroundColor Yellow
    flutter clean
    if ($LASTEXITCODE -ne 0) { throw "Flutter clean failed" }

    # Step 2: Clean Android build
    Write-Host "🧹 Cleaning Android build..." -ForegroundColor Yellow
    Set-Location android
    ./gradlew clean
    if ($LASTEXITCODE -ne 0) { throw "Gradle clean failed" }
    Set-Location ..

    # Step 3: Update dependencies
    Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
    flutter pub get
    if ($LASTEXITCODE -ne 0) { throw "Flutter pub get failed" }

    # Step 4: Build release APK
    Write-Host "🏗️ Building release APK..." -ForegroundColor Yellow
    flutter build apk --release --verbose
    if ($LASTEXITCODE -ne 0) { throw "Flutter build failed" }

    # Step 5: Verify the build
    $apkPath = "build/app/outputs/flutter-apk/app-release.apk"
    if (Test-Path $apkPath) {
        $fileInfo = Get-Item $apkPath
        Write-Host "✅ Build completed successfully!" -ForegroundColor Green
        Write-Host "📱 APK Details:" -ForegroundColor Cyan
        Write-Host "   📍 Location: $apkPath"
        Write-Host "   📦 Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
        Write-Host "   🕒 Built at: $($fileInfo.LastWriteTime)"
    } else {
        throw "APK file not found at expected location"
    }
}
catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor Red
    exit 1
}
