import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/localization.dart';
import '../theme/app_theme.dart';
import '../services/user_data_service.dart';
import '../screens/edit_outlet_screen.dart';


class MyNetworkScreen extends StatefulWidget {
  @override
  _MyNetworkScreenState createState() => _MyNetworkScreenState();
}

class _MyNetworkScreenState extends State<MyNetworkScreen> {
  String? selectedBeat;
  List<String> beats = [];
  Map<String, String> beatToDsMapping = {}; // Store beat to dsName mapping for verification
  List<Map<String, dynamic>> outlets = [];
  List<Map<String, dynamic>> filteredOutlets = [];
  TextEditingController searchController = TextEditingController();
  bool isLoading = false;
  String? _currentUserDsId; // Store current user's dsId for outlet filtering

  // Single scroll controller for the entire table
  final ScrollController _tableScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    fetchBeats();
    searchController.addListener(_filterOutlets);
  }

  @override
  void dispose() {
    searchController.removeListener(_filterOutlets);
    searchController.dispose();
    _tableScrollController.dispose();
    super.dispose();
  }

  void _filterOutlets() {
    final query = searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        filteredOutlets = List.from(outlets);
      } else {
        filteredOutlets = outlets.where((outlet) {
          final outletName = outlet['outletname'].toString().toLowerCase();
          final mobileNumber = outlet['mobileNumber'].toString().toLowerCase();
          return outletName.contains(query) || mobileNumber.contains(query);
        }).toList();
      }
    });
  }

  Future<void> fetchBeats() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();

        // Fetch the user's name and dsId
        String userName = userDoc['name'] ?? ''; // Ensure this field exists in the users collection
        String userDsId = userDoc['dsId'] ?? '';

        // Store the current user's dsId for outlet filtering
        _currentUserDsId = userDsId;

        // Construct the dsName based on user's name and dsId
        String userDsName = '$userName\_$userDsId'; // Assuming the separator is an underscore

        // Fetch beats where dsName matches the constructed userDsName
        QuerySnapshot snapshot = await FirebaseFirestore.instance
            .collection('beats')
            .where('dsName', isEqualTo: userDsName)
            .get();

        // Create a list of beat names and store beat-to-dsName mapping
        List<String> beatList = [];
        Map<String, String> beatMapping = {};

        for (var doc in snapshot.docs) {
          String beatName = doc['beatName'] as String;
          String dsName = doc['dsName'] as String;
          beatList.add(beatName);
          beatMapping[beatName] = dsName;
        }

        // Update state with the filtered beats and mapping
        setState(() {
          beats = beatList;
          beatToDsMapping = beatMapping;
        });
      } else {
        // Handle case where user document ID is not found
        print('User document ID not found');
      }
    } catch (e) {
      print('Error fetching beats: $e');
    }
  }

  Future<void> fetchOutlets(String beat) async {
    setState(() {
      isLoading = true;
    });

    try {
      // Verify that the beat belongs to the current DS user using stored mapping
      if (!beatToDsMapping.containsKey(beat)) {
        print('Beat $beat does not belong to current DS user');
        setState(() {
          outlets = [];
          filteredOutlets = [];
          isLoading = false;
        });
        return;
      }

      // Ensure we have the current user's dsId
      if (_currentUserDsId == null) {
        print('Current user dsId not available');
        setState(() {
          outlets = [];
          filteredOutlets = [];
          isLoading = false;
        });
        return;
      }

      // Fetch outlets for this verified beat that were also created by the current DS user
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('outlets')
          .where('beat', isEqualTo: beat)
          .where('dsId', isEqualTo: _currentUserDsId)
          .get();
      List<Map<String, dynamic>> outletList = snapshot.docs.map((doc) {
        return {
          'outletname': doc['outletName'],
          'outletcode': doc['outletCode'],
          'mobileNumber': doc['mobileNumber'] ?? '',
          'geoLocation': doc['geoLocation'] ?? '',
          'outletAddress': doc['outletAddress'] ?? '',
          'docId': doc.id
        };
      }).toList();

      setState(() {
        outlets = outletList;
        filteredOutlets = List.from(outletList);
        isLoading = false;
      });
    } catch (e) {
      print('Error fetching outlets: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  void _onBeatChanged(String? newBeat) {
    setState(() {
      selectedBeat = newBeat;
    });
    if (newBeat != null) {
      fetchOutlets(newBeat);
    }
  }

  Future<void> _openGoogleMapsDirections(String geoLocation) async {
    if (geoLocation.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.translate('location_not_available'))),
      );
      return;
    }

    final coordinates = geoLocation.split(', ');
    if (coordinates.length != 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.translate('invalid_location_format'))),
      );
      return;
    }

    try {
      // Simple approach using geo: URI scheme which is widely supported on Android
      final uri = Uri.parse('geo:0,0?q=${coordinates[0]},${coordinates[1]}');

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        // Fallback to Google Maps in browser
        final mapUrl = Uri.parse(
          'https://www.google.com/maps/search/?api=1&query=${coordinates[0]},${coordinates[1]}'
        );
        await launchUrl(mapUrl, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      print('Error launching maps: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.translate('maps_app_error'))),
      );
    }
  }

  Future<void> _navigateToEditOutlet(String outletId, String outletName) async {
    // Find the outlet in the filtered list
    final outlet = filteredOutlets.firstWhere(
      (o) => o['docId'] == outletId,
      orElse: () => {'outletAddress': ''},
    );

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditOutletScreen(
          outletId: outletId,
          outletName: outletName,
          outletAddress: outlet['outletAddress'],
        ),
      ),
    );

    // If the outlet was updated, refresh the list
    if (result == true && selectedBeat != null) {
      fetchOutlets(selectedBeat!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('my_network')),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Beat selection dropdown
            DropdownButtonFormField<String>(
              value: selectedBeat,
              hint: Text(context.translate('select_beat')),
              isExpanded: true,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              onChanged: _onBeatChanged,
              items: beats.map<DropdownMenuItem<String>>((String beat) {
                return DropdownMenuItem<String>(
                  value: beat,
                  child: Text(beat),
                );
              }).toList(),
            ),
            SizedBox(height: 24),

            // Header with outlet count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.translate('my_network'),
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${filteredOutlets.length} ${context.translate('outlets')}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Search bar
            TextField(
              controller: searchController,
              decoration: InputDecoration(
                hintText: context.translate('search_outlets'),
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 0),
              ),
            ),
            SizedBox(height: 16),

            // Horizontally scrollable table
            Expanded(
              child: isLoading
                ? Center(child: CircularProgressIndicator())
                : filteredOutlets.isEmpty
                  ? Center(child: Text(context.translate('no_outlets_found')))
                  : Column(
                      children: [
                        // Create a horizontal scrollable container for the entire table
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _tableScrollController,
                            child: SizedBox(
                              width: 450, // Total width of all columns
                              child: Column(
                                children: [
                                  // Table header
                                  Container(
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryColor,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(8),
                                        topRight: Radius.circular(8),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        // Outlet Name header - Flexible
                                        Expanded(
                                          flex: 3,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                                            child: Text(
                                              context.translate('outlet_name'),
                                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
                                            ),
                                          ),
                                        ),
                                        // Phone Number header - Flexible
                                        Expanded(
                                          flex: 2,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                                            child: Text(
                                              context.translate('phone_number'),
                                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
                                            ),
                                          ),
                                        ),
                                        // Location header
                                        Container(
                                          width: 60,
                                          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                                          child: Text(
                                            context.translate('location'),
                                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.white),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        // Edit header
                                        Container(
                                          width: 60,
                                          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                                          child: Text(
                                            context.translate('edit'),
                                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.white),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Table content
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        border: Border.all(color: Colors.grey.shade300),
                                      ),
                                      child: ListView.builder(
                                        itemCount: filteredOutlets.length,
                                        itemBuilder: (context, index) {
                                          final outlet = filteredOutlets[index];
                                          final bool hasLocation = outlet['geoLocation'] != null && outlet['geoLocation'].toString().isNotEmpty;

                                          return Container(
                                            decoration: BoxDecoration(
                                              color: index % 2 == 0 ? Colors.white : Colors.grey.shade50,
                                              border: Border(
                                                bottom: BorderSide(color: Colors.grey.shade300),
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                // Outlet Name - Flexible
                                                Expanded(
                                                  flex: 3,
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                                                    child: Text(
                                                      outlet['outletname'] ?? '',
                                                      style: TextStyle(fontWeight: FontWeight.w500),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ),

                                                // Phone Number - Made clickable
                                                Expanded(
                                                  flex: 2,
                                                  child: Container(
                                                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                                                    child: GestureDetector(
                                                      onTap: () async {
                                                        final phoneNumber = outlet['mobileNumber'] ?? '';
                                                        if (phoneNumber.isNotEmpty) {
                                                          final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
                                                          try {
                                                            if (await canLaunchUrl(phoneUri)) {
                                                              await launchUrl(phoneUri);
                                                            } else {
                                                              ScaffoldMessenger.of(context).showSnackBar(
                                                                SnackBar(content: Text(context.translate('cannot_launch_phone_dialer'))),
                                                              );
                                                            }
                                                          } catch (e) {
                                                            print('Error launching phone dialer: $e');
                                                            ScaffoldMessenger.of(context).showSnackBar(
                                                              SnackBar(content: Text(context.translate('phone_dialer_error'))),
                                                            );
                                                          }
                                                        }
                                                      },
                                                      child: Text(
                                                        outlet['mobileNumber'] ?? '',
                                                        style: TextStyle(
                                                          color: Colors.blue,
                                                          decoration: TextDecoration.underline,
                                                        ),
                                                        overflow: TextOverflow.ellipsis,
                                                      ),
                                                    ),
                                                  ),
                                                ),

                                                // Location with Directions button
                                                Container(
                                                  width: 60,
                                                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                                                  alignment: Alignment.center,
                                                  child: hasLocation
                                                    ? IconButton(
                                                        onPressed: () => _openGoogleMapsDirections(outlet['geoLocation']),
                                                        icon: Icon(Icons.directions, color: Colors.white, size: 16),
                                                        tooltip: context.translate('directions'),
                                                        style: IconButton.styleFrom(
                                                          backgroundColor: Colors.green,
                                                          minimumSize: Size(30, 30),
                                                          padding: EdgeInsets.zero,
                                                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                                        ),
                                                        constraints: BoxConstraints(
                                                          minWidth: 30,
                                                          minHeight: 30,
                                                          maxWidth: 30,
                                                          maxHeight: 30,
                                                        ),
                                                      )
                                                    : Icon(Icons.location_off, color: Colors.grey, size: 16),
                                                ),

                                                // Edit button
                                                Container(
                                                  width: 60,
                                                  padding: EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                                                  alignment: Alignment.center,
                                                  child: IconButton(
                                                    onPressed: () => _navigateToEditOutlet(outlet['docId'], outlet['outletname']),
                                                    icon: Icon(Icons.edit, color: Colors.white, size: 16),
                                                    tooltip: context.translate('edit'),
                                                    style: IconButton.styleFrom(
                                                      backgroundColor: AppTheme.primaryColor,
                                                      minimumSize: Size(30, 30),
                                                      padding: EdgeInsets.zero,
                                                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                                    ),
                                                    constraints: BoxConstraints(
                                                      minWidth: 30,
                                                      minHeight: 30,
                                                      maxWidth: 30,
                                                      maxHeight: 30,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
