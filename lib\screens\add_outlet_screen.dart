import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../theme/app_theme.dart';
import '../utils/localization.dart';
import '../services/user_data_service.dart';

class AddOutletScreen extends StatefulWidget {
  @override
  _AddOutletScreenState createState() => _AddOutletScreenState();
}

class _AddOutletScreenState extends State<AddOutletScreen> {
  final _formKey = GlobalKey<FormState>();
  final _outletNameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _mobileNumberController = TextEditingController();
  final _kycController = TextEditingController();
  final _outletAddressController = TextEditingController();
  final _localityController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  String? _outletType = 'Retail';
  String? _geoLocation;
  String? _selectedBeat;
  List<String> _beats = [];
  String? _dsId; // To store the dsId of the user
  bool _isSubmitting = false;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    // First fetch the DS ID, then fetch beats assigned to this DS
    _fetchDsID().then((_) {
      _fetchBeats();
    });

    // Add listeners to all text controllers to update UI when values change
    _outletNameController.addListener(() => setState(() {}));
    _ownerNameController.addListener(() => setState(() {}));
    _mobileNumberController.addListener(() => setState(() {}));
    _outletAddressController.addListener(() => setState(() {}));
    _localityController.addListener(() => setState(() {}));
    _pincodeController.addListener(() => setState(() {}));
    _cityController.addListener(() => setState(() {}));
    _stateController.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    // Dispose all controllers
    _outletNameController.dispose();
    _ownerNameController.dispose();
    _mobileNumberController.dispose();
    _kycController.dispose();
    _outletAddressController.dispose();
    _localityController.dispose();
    _pincodeController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    super.dispose();
  }

  String? _userName; // To store the user's name for beat filtering

  Future<void> _fetchDsID() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

        setState(() {
          _dsId = userData['dsId'];
          _userName = userData['name'];
        });

        print('Fetched DS ID: $_dsId, User Name: $_userName');
      } else {
        print('User document ID not found');
      }
    } catch (e) {
      print('Error fetching DS ID: $e');
    }
  }

  Future<void> _fetchBeats() async {
    // If DS ID is not available, we can't filter beats
    if (_dsId == null) {
      print('DS ID not available yet, cannot fetch beats');
      return;
    }

    try {
      List<String> beats = [];

      // Construct the dsName (format: userName_dsId)
      String? dsName;
      if (_userName != null) {
        dsName = '${_userName}_${_dsId}';
      }

      // First try to fetch beats by dsName
      if (dsName != null) {
        QuerySnapshot snapshot = await FirebaseFirestore.instance
            .collection('beats')
            .where('dsName', isEqualTo: dsName)
            .get();

        if (snapshot.docs.isNotEmpty) {
          for (var doc in snapshot.docs) {
            beats.add(doc['beatName'] as String);
          }
        }
      }

      // If no beats found with dsName, try fetching by dsId as fallback
      if (beats.isEmpty) {
        QuerySnapshot snapshot = await FirebaseFirestore.instance
            .collection('beats')
            .where('dsId', isEqualTo: _dsId)
            .get();

        for (var doc in snapshot.docs) {
          beats.add(doc['beatName'] as String);
        }
      }

      // If still no beats found, show a message
      if (beats.isEmpty) {
        print('No beats assigned to this DS (ID: $_dsId, Name: $_userName)');
      } else {
        print('Fetched ${beats.length} beats for DS ID: $_dsId');
      }

      setState(() {
        _beats = beats;
      });
    } catch (e) {
      print('Failed to fetch beats: $e');
    }
  }

  Future<void> _getGeoLocation() async {
    // Prevent multiple button presses
    if (_isGettingLocation) return;

    setState(() {
      _isGettingLocation = true;
    });

    if (await Permission.location.request().isGranted) {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Location services are disabled.')),
        );
        setState(() {
          _isGettingLocation = false;
        });
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Location permissions are denied')),
          );
          setState(() {
            _isGettingLocation = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Location permissions are permanently denied, we cannot request permissions.')),
        );
        setState(() {
          _isGettingLocation = false;
        });
        return;
      }

      Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      setState(() {
        _geoLocation = '${position.latitude}, ${position.longitude}';
        _isGettingLocation = false;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Location permissions are denied')),
      );
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  Future<void> _fetchLocationDetails(String pincode) async {
    final url = 'https://api.postalpincode.in/pincode/$pincode';
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data[0]['Status'] == 'Success') {
          final place = data[0]['PostOffice'][0];
          setState(() {
            _cityController.text = place['District'];
            _stateController.text = place['State'];
          });
        } else {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Invalid pincode. Please try again.'),
          ));
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Invalid pincode. Please try again.'),
        ));
      }
    } catch (e) {
      print('Failed to fetch location details: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Failed to fetch location details. Please try again.'),
      ));
    }
  }

  Future<String> _generateOutletCode() async {
    final stateCode = _stateController.text.substring(0, 3).toUpperCase();

    final QuerySnapshot querySnapshot = await FirebaseFirestore.instance
        .collection('outlets')
        .where('outletCode', isGreaterThanOrEqualTo: 'QK-OUTLET-$stateCode-')
        .where('outletCode', isLessThanOrEqualTo: 'QK-OUTLET-$stateCode-\uf8ff')
        .orderBy('outletCode', descending: true)
        .limit(1)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      String lastOutletCode = querySnapshot.docs.first['outletCode'];
      int lastIdNumber = int.parse(lastOutletCode.split('-').last);
      int newIdNumber = lastIdNumber + 1;
      return 'QK-OUTLET-$stateCode-${newIdNumber.toString().padLeft(6, '0')}';
    } else {
      return 'QK-OUTLET-$stateCode-000001';
    }
  }

  // Check if form is valid
  Future<void> _addOutlet() async {
    // Prevent multiple button presses
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    if (_formKey.currentState!.validate()) {
      if (_geoLocation == null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Location is not fetched. Please try again.'),
        ));
        setState(() {
          _isSubmitting = false;
        });
        return;
      }
      try {
        String outletCode = await _generateOutletCode();
        await FirebaseFirestore.instance.collection('outlets').add({
          'outletName': _outletNameController.text,
          'ownerName': _ownerNameController.text,
          'mobileNumber': _mobileNumberController.text,
          'kyc': _kycController.text,
          'outletAddress': _outletAddressController.text,
          'locality': _localityController.text,
          'pincode': _pincodeController.text,
          'city': _cityController.text,
          'state': _stateController.text,
          'beat': _selectedBeat,
          'outletType': _outletType,
          'geoLocation': _geoLocation,
          'outletCode': outletCode,
          'dsId': _dsId, // Store the dsId of the user who created the outlet
          'status': 'active', // Set the status as active
          'createdAt': FieldValue.serverTimestamp(), // Add creation timestamp
        });

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Outlet added successfully')));
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to add outlet: $e')));
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  // Check if all mandatory fields are filled
  bool _isFormValid() {
    // Check text fields
    if (_outletNameController.text.isEmpty ||
        _ownerNameController.text.isEmpty ||
        _mobileNumberController.text.length != 10 ||
        _outletAddressController.text.isEmpty ||
        _localityController.text.isEmpty ||
        _pincodeController.text.length != 6 ||
        _cityController.text.isEmpty ||
        _stateController.text.isEmpty) {
      return false;
    }

    // Check dropdown and radio button
    if (_selectedBeat == null || _outletType == null) {
      return false;
    }

    // Check location
    if (_geoLocation == null) {
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('add_outlet')),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withOpacity(0.05),
              Colors.white,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section
                  Container(
                    margin: EdgeInsets.only(bottom: 24),
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.store_mall_directory,
                            color: AppTheme.primaryColor,
                            size: 28,
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                context.translate('add_outlet'),
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Fill in the details to add a new outlet',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppTheme.textSecondaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Required fields note
                  Container(
                    margin: EdgeInsets.only(bottom: 16),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, size: 16, color: AppTheme.textSecondaryColor),
                        SizedBox(width: 8),
                        Text(
                          context.translate('mandatory_fields_note'),
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Form fields in a card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic Information Section
                          Container(
                            margin: EdgeInsets.only(bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.translate('basic_information'),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                Divider(),
                                SizedBox(height: 8),
                                TextFormField(
                                  controller: _outletNameController,
                                  decoration: InputDecoration(
                                    labelText: '${context.translate('outlet_name')} *',
                                    prefixIcon: Icon(Icons.store, color: AppTheme.primaryColor),
                                    border: OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter the outlet name';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 16),
                                TextFormField(
                                  controller: _ownerNameController,
                                  decoration: InputDecoration(
                                    labelText: '${context.translate('owner_name')} *',
                                    prefixIcon: Icon(Icons.person, color: AppTheme.primaryColor),
                                    border: OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter the owner name';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 16),
                                TextFormField(
                                  controller: _mobileNumberController,
                                  decoration: InputDecoration(
                                    labelText: '${context.translate('mobile_number')} *',
                                    prefixIcon: Icon(Icons.phone, color: AppTheme.primaryColor),
                                    border: OutlineInputBorder(),
                                    counterText: '', // Hide the counter
                                  ),
                                  keyboardType: TextInputType.phone,
                                  maxLength: 10, // Set maximum length to 10
                                  inputFormatters: [
                                    FilteringTextInputFormatter.digitsOnly, // Allow only digits
                                    LengthLimitingTextInputFormatter(10), // Limit to 10 digits
                                  ],
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter the mobile number';
                                    }
                                    if (value.length != 10) {
                                      return 'Please enter a valid 10-digit mobile number';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 16),
                                TextFormField(
                                  controller: _kycController,
                                  decoration: InputDecoration(
                                    labelText: context.translate('kyc_details'),
                                    prefixIcon: Icon(Icons.badge, color: AppTheme.primaryColor),
                                    border: OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    return null; // Optional field
                                  },
                                ),
                              ],
                            ),
                          ),

                          // Address Information Section
                          Container(
                            margin: EdgeInsets.only(bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.translate('address_information'),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                Divider(),
                                SizedBox(height: 8),
                                TextFormField(
                                  controller: _outletAddressController,
                                  decoration: InputDecoration(
                                    labelText: '${context.translate('outlet_address')} *',
                                    prefixIcon: Icon(Icons.location_on, color: AppTheme.primaryColor),
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 2,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter the outlet address';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: 16),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        controller: _localityController,
                                        decoration: InputDecoration(
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          label: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(context.translate('locality'), style: TextStyle(fontSize: 14)),
                                              Text(' *', style: TextStyle(color: Colors.red, fontSize: 14)),
                                            ],
                                          ),
                                          prefixIcon: Icon(Icons.location_city, color: AppTheme.primaryColor),
                                          border: OutlineInputBorder(),
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter the locality';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 16),
                                    Expanded(
                                      child: TextFormField(
                                        controller: _pincodeController,
                                        decoration: InputDecoration(
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          label: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(context.translate('pincode'), style: TextStyle(fontSize: 14)),
                                              Text(' *', style: TextStyle(color: Colors.red, fontSize: 14)),
                                            ],
                                          ),
                                          prefixIcon: Icon(Icons.pin, color: AppTheme.primaryColor),
                                          border: OutlineInputBorder(),
                                          counterText: '', // Hide the counter
                                        ),
                                        keyboardType: TextInputType.number,
                                        maxLength: 6, // Set maximum length to 6
                                        inputFormatters: [
                                          FilteringTextInputFormatter.digitsOnly, // Allow only digits
                                          LengthLimitingTextInputFormatter(6), // Limit to 6 digits
                                        ],
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter the pincode';
                                          }
                                          if (value.length != 6) {
                                            return 'Please enter a valid 6-digit pincode';
                                          }
                                          return null;
                                        },
                                        onChanged: (value) {
                                          if (value.length == 6) {
                                            _fetchLocationDetails(value);
                                          }
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 16),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        controller: _cityController,
                                        decoration: InputDecoration(
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          label: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(context.translate('city'), style: TextStyle(fontSize: 14)),
                                              Text(' *', style: TextStyle(color: Colors.red, fontSize: 14)),
                                            ],
                                          ),
                                          prefixIcon: Icon(Icons.location_city, color: AppTheme.primaryColor),
                                          border: OutlineInputBorder(),
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter the city';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 16),
                                    Expanded(
                                      child: TextFormField(
                                        controller: _stateController,
                                        decoration: InputDecoration(
                                          floatingLabelBehavior: FloatingLabelBehavior.always,
                                          label: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(context.translate('state'), style: TextStyle(fontSize: 14)),
                                              Text(' *', style: TextStyle(color: Colors.red, fontSize: 14)),
                                            ],
                                          ),
                                          prefixIcon: Icon(Icons.map, color: AppTheme.primaryColor),
                                          border: OutlineInputBorder(),
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please enter the state';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // Additional Information Section
                          Container(
                            margin: EdgeInsets.only(bottom: 16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  context.translate('additional_information'),
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                Divider(),
                                SizedBox(height: 8),
                                _beats.isEmpty
                                ? Container(
                                    padding: EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.red.shade300),
                                      borderRadius: BorderRadius.circular(4),
                                      color: Colors.red.shade50,
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.warning, color: Colors.red),
                                        SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            'No beats are assigned to you. Please contact your administrator.',
                                            style: TextStyle(color: Colors.red.shade700),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : DropdownButtonFormField<String>(
                                    decoration: InputDecoration(
                                      labelText: '${context.translate('beat')} *',
                                      prefixIcon: Icon(Icons.route, color: AppTheme.primaryColor),
                                      border: OutlineInputBorder(),
                                      hintText: 'Select a beat assigned to you',
                                    ),
                                    value: _selectedBeat,
                                    items: _beats
                                        .map((beat) => DropdownMenuItem<String>(
                                      value: beat,
                                      child: Text(beat),
                                    ))
                                        .toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedBeat = value;
                                      });
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please select the beat';
                                      }
                                      return null;
                                    },
                                  ),
                                SizedBox(height: 16),
                                Text(
                                  '${context.translate('outlet_type')} *',
                                  style: TextStyle(fontSize: 16),
                                ),
                                SizedBox(height: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: RadioListTile<String>(
                                          title: Text(context.translate('retail')),
                                          value: 'Retail',
                                          groupValue: _outletType,
                                          onChanged: (value) {
                                            setState(() {
                                              _outletType = value;
                                            });
                                          },
                                          activeColor: AppTheme.primaryColor,
                                        ),
                                      ),
                                      Expanded(
                                        child: RadioListTile<String>(
                                          title: Text(context.translate('swd')),
                                          value: 'SWD',
                                          groupValue: _outletType,
                                          onChanged: (value) {
                                            setState(() {
                                              _outletType = value;
                                            });
                                          },
                                          activeColor: AppTheme.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Location Section
                          Container(
                            margin: EdgeInsets.only(bottom: 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${context.translate('location')} *',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                                Divider(),
                                SizedBox(height: 8),
                                Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: _geoLocation != null ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: _geoLocation != null ? Colors.green : Colors.grey.shade400,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            _geoLocation != null ? Icons.check_circle : Icons.location_searching,
                                            color: _geoLocation != null ? Colors.green : Colors.grey,
                                          ),
                                          SizedBox(width: 8),
                                          Text(
                                            _geoLocation != null ? context.translate('location_acquired') : context.translate('location_required'),
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: _geoLocation != null ? Colors.green : Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (_geoLocation != null) ...[
                                        SizedBox(height: 8),
                                        Text(
                                          '${context.translate('coordinates')}: $_geoLocation',
                                          style: TextStyle(fontSize: 12),
                                        ),
                                      ],
                                      SizedBox(height: 16),
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton.icon(
                                          onPressed: _isGettingLocation ? null : _getGeoLocation,
                                          icon: _isGettingLocation
                                              ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                                              : Icon(_geoLocation == null ? Icons.location_on : Icons.refresh),
                                          label: Text(_isGettingLocation
                                              ? 'Getting location...'
                                              : (_geoLocation == null ? context.translate('get_location') : context.translate('refresh_location'))),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: _isGettingLocation
                                                ? Colors.grey
                                                : (_geoLocation != null ? Colors.green : AppTheme.primaryColor),
                                            foregroundColor: Colors.white,
                                            padding: EdgeInsets.symmetric(vertical: 12),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Submit Button
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton.icon(
                              onPressed: _isSubmitting || !_isFormValid() ? null : _addOutlet,
                              icon: _isSubmitting
                                  ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                                  : Icon(Icons.add_business),
                              label: Text(
                                _isSubmitting ? 'Submitting...' : context.translate('submit'),
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, letterSpacing: 1),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _isSubmitting || !_isFormValid() ? Colors.grey : AppTheme.primaryColor,
                                foregroundColor: Colors.white,
                                disabledBackgroundColor: Colors.grey.shade400,
                                elevation: 2,
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
