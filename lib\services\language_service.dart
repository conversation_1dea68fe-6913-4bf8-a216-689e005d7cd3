import 'package:flutter/material.dart';
import 'dart:async';

class LanguageService {
  static final LanguageService _instance = LanguageService._internal();
  factory LanguageService() => _instance;
  LanguageService._internal();

  static const String LANGUAGE_CODE = 'languageCode';
  static const String COUNTRY_CODE = 'countryCode';

  // Supported languages
  static const Locale ENGLISH = Locale('en', 'US');
  static const Locale HINDI = Locale('hi', 'IN');

  // Default language
  static const Locale DEFAULT_LANGUAGE = ENGLISH;

  // List of supported locales
  static const List<Locale> SUPPORTED_LOCALES = [ENGLISH, HINDI];

  // Current locale
  Locale _currentLocale = DEFAULT_LANGUAGE;
  Locale get currentLocale => _currentLocale;

  // Stream controller for language changes
  final _localeController = ValueNotifier<Locale>(DEFAULT_LANGUAGE);
  ValueNotifier<Locale> get localeStream => _localeController;

  // Initialize language service
  Future<void> init() async {
    try {
      print('Initializing language service...');
      // Default to English
      _currentLocale = DEFAULT_LANGUAGE;
      _localeController.value = _currentLocale;
      print('Language initialized: ${_currentLocale.languageCode}_${_currentLocale.countryCode}');
    } catch (e) {
      print('Error initializing language: $e');
      _currentLocale = DEFAULT_LANGUAGE;
      _localeController.value = _currentLocale;
    }
  }

  // Change language
  Future<void> changeLanguage(Locale locale) async {
    try {
      print('Changing language to: ${locale.languageCode}_${locale.countryCode}');

      if (!SUPPORTED_LOCALES.contains(locale)) {
        print('Language not supported: ${locale.languageCode}_${locale.countryCode}');
        return;
      }

      // Update the locale in memory
      _currentLocale = locale;

      // Notify listeners
      try {
        _localeController.value = locale;
      } catch (e) {
        print('Error updating locale controller: $e');
      }

      print('Language changed successfully to: ${locale.languageCode}_${locale.countryCode}');
    } catch (e) {
      print('Error changing language: $e');
    }
  }

  // Toggle between English and Hindi
  Future<void> toggleLanguage() async {
    if (_currentLocale.languageCode == ENGLISH.languageCode) {
      await changeLanguage(HINDI);
    } else {
      await changeLanguage(ENGLISH);
    }
  }

  // Get language name
  String getLanguageName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'hi':
        return 'हिंदी';
      default:
        return 'English';
    }
  }

  // Get current language name
  String get currentLanguageName => getLanguageName(_currentLocale);

  // Check if current language is RTL
  bool get isRTL => false; // Hindi is not RTL
}
