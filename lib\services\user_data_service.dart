import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserDataService {
  static final UserDataService _instance = UserDataService._internal();
  
  factory UserDataService() {
    return _instance;
  }
  
  UserDataService._internal();
  
  // Key for storing the document ID in SharedPreferences
  static const String _userDocIdKey = 'user_document_id';
  
  // Cache the document ID in memory
  String? _cachedUserDocId;
  
  // Get the user document ID (either from cache, SharedPreferences, or Firebase Auth)
  Future<String?> getUserDocumentId() async {
    // If we have a cached value, return it
    if (_cachedUserDocId != null) {
      return _cachedUserDocId;
    }
    
    // Try to get the value from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? storedDocId = prefs.getString(_userDocIdKey);
    
    if (storedDocId != null) {
      _cachedUserDocId = storedDocId;
      return storedDocId;
    }
    
    // If not found in SharedPreferences, use the Firebase Auth UID
    User? user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      return user.uid;
    }
    
    return null;
  }
  
  // Set the user document ID
  Future<void> setUserDocumentId(String docId) async {
    // Cache the value
    _cachedUserDocId = docId;
    
    // Store in SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userDocIdKey, docId);
    
    print('User document ID set to: $docId');
  }
  
  // Clear the user document ID (e.g., on logout)
  Future<void> clearUserDocumentId() async {
    // Clear the cache
    _cachedUserDocId = null;
    
    // Clear from SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userDocIdKey);
    
    print('User document ID cleared');
  }
  
  // Get a reference to the user's document
  Future<DocumentReference?> getUserDocumentReference() async {
    String? docId = await getUserDocumentId();
    if (docId != null) {
      return FirebaseFirestore.instance.collection('users').doc(docId);
    }
    return null;
  }
  
  // Get the user document data
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      String? docId = await getUserDocumentId();
      if (docId != null) {
        DocumentSnapshot doc = await FirebaseFirestore.instance.collection('users').doc(docId).get();
        if (doc.exists) {
          return doc.data() as Map<String, dynamic>?;
        }
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }
}
