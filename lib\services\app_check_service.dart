import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../config/app_check_config.dart';

class AppCheckService {
  static const String _lastTokenKey = 'last_app_check_token';
  static const String _tokenTimestampKey = 'app_check_token_timestamp';
  static const String _deviceFingerprintKey = 'device_fingerprint';
  static const String _rateLimitKey = 'app_check_rate_limit';
  static const String _rateLimitCountKey = 'app_check_rate_limit_count';
  static const String _rateLimitTimestampKey = 'app_check_rate_limit_timestamp';
  
  final SharedPreferences _prefs;
  
  AppCheckService._(this._prefs);
  
  static Future<AppCheckService> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final service = AppCheckService._(prefs);
    await service._initializeDeviceFingerprint();
    return service;
  }

  Future<void> _initializeDeviceFingerprint() async {
    if (_prefs.getString(_deviceFingerprintKey) != null) return;
    
    final deviceInfo = DeviceInfoPlugin();
    final androidInfo = await deviceInfo.androidInfo;
    
    final fingerprint = {
      'manufacturer': androidInfo.manufacturer,
      'model': androidInfo.model,
      'androidId': androidInfo.id,
      'fingerprint': androidInfo.fingerprint,
      'securityPatch': androidInfo.version.securityPatch,
      'sdkInt': androidInfo.version.sdkInt,
      'firstSeen': DateTime.now().toIso8601String(),
    };
    
    await _prefs.setString(_deviceFingerprintKey, json.encode(fingerprint));
  }

  Future<void> validateAndRefreshToken({bool force = false}) async {
    try {
      // Check cached token first unless forced refresh
      if (!force) {
        final cachedToken = await _getCachedToken();
        final timestamp = _prefs.getInt(_tokenTimestampKey);
        if (cachedToken != null && timestamp != null) {
          final age = DateTime.now().millisecondsSinceEpoch - timestamp;
          if (age < AppCheckConfig.maxTokenAge.inMilliseconds) {
            debugPrint('✅ Using valid cached App Check token');
            return;
          }
        }
      }

      // Try with rate limiting
      await _enforceRateLimit();

      // Try Play Integrity first if available
      try {
        final token = await FirebaseAppCheck.instance.getToken(force);
        if (token != null) {
          await _saveToken(token);
          await _updateRateLimit();
          debugPrint('✅ App Check token refreshed via Play Integrity');
          return;
        }
      } catch (e) {
        debugPrint('⚠️ Play Integrity token refresh failed: $e');
      }

      // Fallback to custom token
      try {
        final customToken = await _getCustomToken();
        if (customToken != null) {
          await _saveToken(customToken);
          await _updateRateLimit();
          debugPrint('✅ App Check token refreshed via custom provider');
          return;
        }
      } catch (e) {
        debugPrint('⚠️ Custom token refresh failed: $e');
        rethrow;
      }
    } catch (e) {
      debugPrint('❌ All token refresh attempts failed: $e');
      rethrow;
    }
  }

  Future<String?> _getCachedToken() async {
    return _prefs.getString(_lastTokenKey);
  }

  Future<void> _enforceRateLimit() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final lastCheck = _prefs.getInt(_rateLimitTimestampKey) ?? 0;
    final tokenCount = _prefs.getInt(_rateLimitCountKey) ?? 0;

    // Reset counter if window has passed
    if (now - lastCheck > AppCheckConfig.rateLimitWindow.inMilliseconds) {
      await _prefs.setInt(_rateLimitCountKey, 0);
      await _prefs.setInt(_rateLimitTimestampKey, now);
      return;
    }

    // Check rate limit
    if (tokenCount >= AppCheckConfig.maxTokensPerHour) {
      throw Exception(AppCheckConfig.errorRateLimitExceeded);
    }
  }

  Future<void> _updateRateLimit() async {
    final count = _prefs.getInt(_rateLimitCountKey) ?? 0;
    await _prefs.setInt(_rateLimitCountKey, count + 1);
  }

  Future<bool> validateTokenWithBackend(String token) async {
    try {
      final response = await http.post(
        Uri.parse(AppCheckConfig.validateTokenEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-Info': _prefs.getString(_deviceFingerprintKey) ?? '',
        },
        body: json.encode({'token': token}),
      ).timeout(AppCheckConfig.tokenRequestTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['valid'] == true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ Token validation failed: $e');
      return false;
    }
  }

  Future<bool> _isTokenValid(String token) async {
    // Check token format
    if (token.isEmpty) return false;

    // Check if token is expired based on timestamp
    final timestamp = _prefs.getInt(_tokenTimestampKey);
    if (timestamp == null) return false;

    final age = DateTime.now().millisecondsSinceEpoch - timestamp;
    if (age > AppCheckConfig.tokenLifetime.inMilliseconds) {
      return false;
    }

    // Validate with backend
    return await validateTokenWithBackend(token);
  }

  Future<void> _saveToken(String token) async {
    await _prefs.setString(_lastTokenKey, token);
    await _prefs.setInt(_tokenTimestampKey, DateTime.now().millisecondsSinceEpoch);
  }

  Future<String?> _getCustomToken() async {
    // Implementation deferred to CustomAppCheckProvider
    return null;
  }

  static Stream<String?> get tokenChanges {
    return FirebaseAppCheck.instance.onTokenChange;
  }
}
