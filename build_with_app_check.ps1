# Function to generate a debug token
function Generate-DebugToken {
    $randomBytes = New-Object Byte[] 32
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($randomBytes)
    $debugToken = [Convert]::ToBase64String($randomBytes).Substring(0, 32)
    return $debugToken
}

# Function to update local.properties
function Update-LocalProperties {
    param (
        [string]$debugToken
    )
    
    $localPropertiesPath = "android/local.properties"
    $content = Get-Content $localPropertiesPath -ErrorAction SilentlyContinue
    $updated = $false
    
    if ($content) {
        $newContent = $content | ForEach-Object {
            if ($_ -match "^appCheckDebugToken=") {
                $updated = $true
                "appCheckDebugToken=$debugToken"
            } else {
                $_
            }
        }
        if (-not $updated) {
            $newContent += "appCheckDebugToken=$debugToken"
        }
    } else {
        $newContent = "appCheckDebugToken=$debugToken"
    }
    
    $newContent | Set-Content $localPropertiesPath -Force
}

Write-Host "Configuring Firebase App Check..."
$debugToken = Generate-DebugToken
Update-LocalProperties -debugToken $debugToken
Write-Host "Debug token configured: $debugToken"

Write-Host "Cleaning project..."
flutter clean

Write-Host "Getting dependencies..."
flutter pub get

Write-Host "Cleaning Android build..."
Push-Location android
.\gradlew clean
Pop-Location

Write-Host "Building release APK..."
flutter build apk --release

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!"
    Write-Host "APK location: build/app/outputs/flutter-apk/app-release.apk"
} else {
    Write-Host "Build failed with exit code $LASTEXITCODE"
    exit $LASTEXITCODE
}
