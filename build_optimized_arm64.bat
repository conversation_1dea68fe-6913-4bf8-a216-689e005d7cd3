@echo off
echo Building optimized ARM64 APK...

REM Clean the project
flutter clean

REM Get dependencies
flutter pub get

REM Build optimized APK for ARM64 devices
echo Building optimized ARM64 APK...
flutter build apk --release --target-platform=android-arm64 --obfuscate --split-debug-info=build/debug-info --tree-shake-icons

echo Build completed!
echo APK is available in build\app\outputs\apk\release\quickk-ds-panel.apk
