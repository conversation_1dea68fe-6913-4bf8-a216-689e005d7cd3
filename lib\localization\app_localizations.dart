import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/file_utils.dart';

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  // Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // Static member to have a simple access to the delegate from the MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  Future<bool> load() async {
    try {
      // Load the language JSON file from the "lang" folder
      print('Loading language file: assets/lang/${locale.languageCode}.json');
      String jsonString;

      try {
        // First try to load from assets bundle
        jsonString = await rootBundle.loadString('assets/lang/${locale.languageCode}.json')
            .timeout(Duration(seconds: 2), onTimeout: () {
          print('Loading language file timed out, trying fallback');
          throw TimeoutException('Loading language file timed out');
        });
      } catch (loadError) {
        print('Error loading language file from assets: $loadError');

        // Try to load from local file
        print('Trying to load from local file: ${locale.languageCode}.json');
        String? localContent = await FileUtils.readLocalFile('${locale.languageCode}.json');

        if (localContent != null && localContent.isNotEmpty) {
          print('Successfully loaded from local file');
          jsonString = localContent;
        } else {
          print('Failed to load from local file, trying to copy assets to local');

          // Try to copy assets to local files and then load
          bool copied = await FileUtils.copyLanguageFilesToLocal();
          if (copied) {
            localContent = await FileUtils.readLocalFile('${locale.languageCode}.json');
            if (localContent != null && localContent.isNotEmpty) {
              print('Successfully loaded from newly copied local file');
              jsonString = localContent;
            } else {
              throw Exception('Failed to load from newly copied local file');
            }
          } else {
            // Try to load English as fallback
            if (locale.languageCode != 'en') {
              print('Trying to load English as fallback');
              try {
                jsonString = await rootBundle.loadString('assets/lang/en.json');
                print('Loaded English fallback successfully');
              } catch (fallbackError) {
                print('Failed to load English fallback: $fallbackError');
                // Use hardcoded fallback values
                _localizedStrings = {
                  'app_name': 'Quickk DS',
                  'dashboard': 'Dashboard',
                  'notifications': 'Notifications',
                  'no_notifications': 'No notifications yet',
                  'clean_up': 'Clean Up',
                  'cancel': 'Cancel',
                  'error': 'Error',
                  'success': 'Success',
                  'test': 'Test',
                  'press_back_again_to_exit': 'Press back again to exit',
                  'location_services_disabled': 'Location services are disabled.',
                  'location_permissions_denied': 'Location permissions are denied',
                  'location_permissions_permanently_denied': 'Location permissions are permanently denied.',
                  'clean_up_notifications': 'Clean Up Notifications',
                  'clean_up_notifications_confirm': 'This will remove all read notifications older than 7 days. Continue?',
                  'old_notifications_cleaned_up': 'Old notifications cleaned up'
                };
                return false;
              }
            } else {
              // Use hardcoded fallback values for English
              _localizedStrings = {
                'app_name': 'Quickk DS',
                'dashboard': 'Dashboard',
                'notifications': 'Notifications',
                'no_notifications': 'No notifications yet',
                'clean_up': 'Clean Up',
                'cancel': 'Cancel',
                'error': 'Error',
                'success': 'Success',
                'test': 'Test',
                'press_back_again_to_exit': 'Press back again to exit',
                'location_services_disabled': 'Location services are disabled.',
                'location_permissions_denied': 'Location permissions are denied',
                'location_permissions_permanently_denied': 'Location permissions are permanently denied.',
                'clean_up_notifications': 'Clean Up Notifications',
                'clean_up_notifications_confirm': 'This will remove all read notifications older than 7 days. Continue?',
                'old_notifications_cleaned_up': 'Old notifications cleaned up'
              };
              return false;
            }
          }
        }
      }

      print('Loaded language file successfully, length: ${jsonString.length}');

      Map<String, dynamic> jsonMap;
      try {
        jsonMap = json.decode(jsonString);
        print('Decoded JSON successfully, keys: ${jsonMap.keys.length}');
      } catch (jsonError) {
        print('Error decoding JSON: $jsonError');
        // Use hardcoded fallback values
        _localizedStrings = {
          'app_name': 'Quickk DS',
          'dashboard': 'Dashboard',
          'notifications': 'Notifications',
          'no_notifications': 'No notifications yet',
          'clean_up': 'Clean Up',
          'cancel': 'Cancel',
          'error': 'Error',
          'success': 'Success',
          'test': 'Test',
          'press_back_again_to_exit': 'Press back again to exit',
          'location_services_disabled': 'Location services are disabled.',
          'location_permissions_denied': 'Location permissions are denied',
          'location_permissions_permanently_denied': 'Location permissions are permanently denied.',
          'clean_up_notifications': 'Clean Up Notifications',
          'clean_up_notifications_confirm': 'This will remove all read notifications older than 7 days. Continue?',
          'old_notifications_cleaned_up': 'Old notifications cleaned up'
        };
        return false;
      }

      _localizedStrings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      return true;
    } catch (e) {
      print('Error in load method: $e');
      // Initialize with basic fallback values to avoid crashes
      _localizedStrings = {
        'app_name': 'Quickk DS',
        'dashboard': 'Dashboard',
        'notifications': 'Notifications',
        'no_notifications': 'No notifications yet',
        'clean_up': 'Clean Up',
        'cancel': 'Cancel',
        'error': 'Error',
        'success': 'Success',
        'test': 'Test',
        'press_back_again_to_exit': 'Press back again to exit',
        'location_services_disabled': 'Location services are disabled.',
        'location_permissions_denied': 'Location permissions are denied',
        'location_permissions_permanently_denied': 'Location permissions are permanently denied.',
        'clean_up_notifications': 'Clean Up Notifications',
        'clean_up_notifications_confirm': 'This will remove all read notifications older than 7 days. Continue?',
        'old_notifications_cleaned_up': 'Old notifications cleaned up'
      };
      return false;
    }
  }

  // This method will be called from every widget which needs a localized text
  String translate(String key) {
    try {
      if (_localizedStrings.containsKey(key)) {
        return _localizedStrings[key]!;
      } else {
        print('Missing translation for key: $key in ${locale.languageCode}');
        return key;
      }
    } catch (e) {
      print('Error translating key: $key, error: $e');
      return key;
    }
  }
}

// LocalizationsDelegate is a factory for a set of localized resources
class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  // This delegate instance will never change (it doesn't even have fields!)
  // It can provide a constant constructor.
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Include all of your supported language codes here
    return ['en', 'hi'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    try {
      print('Loading localizations for locale: ${locale.languageCode}');
      // AppLocalizations class is where the JSON loading actually runs
      AppLocalizations localizations = AppLocalizations(locale);
      bool success = await localizations.load();
      print('Loaded localizations successfully: $success');
      return localizations;
    } catch (e) {
      print('Error in delegate load: $e');
      // Return a default localization to avoid crashes
      return AppLocalizations(Locale('en', 'US'));
    }
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
