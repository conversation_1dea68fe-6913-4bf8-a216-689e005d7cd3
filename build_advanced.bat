@echo off
echo Building optimized APK with advanced options...

REM Clean the project
flutter clean

REM Get dependencies
flutter pub get

REM Choose build type
echo Select build type:
echo 1. Standard APK (most compatible)
echo 2. Split APKs by architecture (smaller downloads)
echo 3. App Bundle for Google Play (smallest downloads)
echo 4. All of the above

set /p build_type="Enter option (1-4): "

if "%build_type%"=="1" (
    echo Building standard APK...
    flutter build apk --release
    echo Standard APK is available in build/app/outputs/flutter-apk/app-release.apk
)

if "%build_type%"=="2" (
    echo Building split APKs...
    flutter build apk --release --split-per-abi
    echo Split APKs are available in build/app/outputs/flutter-apk/
)

if "%build_type%"=="3" (
    echo Building App Bundle...
    flutter build appbundle --release
    echo App Bundle is available in build/app/outputs/bundle/release/
)

if "%build_type%"=="4" (
    echo Building all formats...
    
    echo Building standard APK...
    flutter build apk --release
    
    echo Building split APKs...
    flutter build apk --release --split-per-abi
    
    echo Building App Bundle...
    flutter build appbundle --release
    
    echo All builds completed!
    echo Standard APK is available in build/app/outputs/flutter-apk/app-release.apk
    echo Split APKs are available in build/app/outputs/flutter-apk/
    echo App Bundle is available in build/app/outputs/bundle/release/
)

echo Build process completed!
