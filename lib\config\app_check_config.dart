class AppCheckConfig {
  // Backend Configuration
  static const String backendUrl = 'https://generateappchecktoken-5yw2qsexaa-uc.a.run.app';
  static const String appSecret = 'quickk-ds-panel-custom-secret-2024';
  static const String appSignature = '6b:0a:d1:89:f9:17:af:e9:8f:ab:06:2e:fd:e1:7a:ce:0c:89:fd:00:8d:0e:88:00:8d:54:e3:37:c6:23:cb:9c';
  
  // Token Settings
  static const Duration tokenLifetime = Duration(hours: 1);
  static const Duration tokenRefreshInterval = Duration(minutes: 50);
  static const Duration tokenRequestTimeout = Duration(seconds: 10);
  
  // Retry Settings
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  static const Duration maxTokenAge = Duration(minutes: 5);
  
  // Rate Limiting
  static const int maxTokensPerHour = 10;
  static const Duration rateLimitWindow = Duration(hours: 1);
  
  // API Endpoints
  static String get generateTokenEndpoint => '$backendUrl/generateAppCheckToken';
  static String get validateTokenEndpoint => '$backendUrl/validateAppCheckToken';
  
  // Debug Settings
  static bool get isDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }
  static const bool enableDebugLogs = false;
  
  // Error Messages
  static const String errorPlayIntegrityFailed = 'Play Integrity verification failed';
  static const String errorCustomTokenFailed = 'Custom token generation failed';
  static const String errorAllProvidersFailed = 'All App Check providers failed';
  static const String errorTokenExpired = 'App Check token has expired';
  static const String errorInvalidDevice = 'Device verification failed';
  static const String errorRateLimitExceeded = 'Token generation rate limit exceeded';
}
