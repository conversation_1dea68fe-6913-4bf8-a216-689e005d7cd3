import 'package:flutter/material.dart';
import '../services/language_service.dart';

class SimpleLanguageToggle extends StatefulWidget {
  @override
  _SimpleLanguageToggleState createState() => _SimpleLanguageToggleState();
}

class _SimpleLanguageToggleState extends State<SimpleLanguageToggle> {
  final LanguageService _languageService = LanguageService();
  
  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.language),
      tooltip: 'Change Language',
      onSelected: (String languageCode) {
        // Change language
        if (languageCode == 'en') {
          _languageService.changeLanguage(LanguageService.ENGLISH);
        } else if (languageCode == 'hi') {
          _languageService.changeLanguage(LanguageService.HINDI);
        }
        
        // Update UI
        setState(() {});
        
        // Show message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Language changed to ${_languageService.getLanguageName(
              _languageService.currentLocale
            )}'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'en',
          child: Row(
            children: [
              Text('EN', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(width: 8),
              Text('English'),
              if (_languageService.currentLocale.languageCode == 'en')
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Icon(Icons.check, size: 16, color: Colors.green),
                ),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'hi',
          child: Row(
            children: [
              Text('HI', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(width: 8),
              Text('Hindi'),
              if (_languageService.currentLocale.languageCode == 'hi')
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Icon(Icons.check, size: 16, color: Colors.green),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
