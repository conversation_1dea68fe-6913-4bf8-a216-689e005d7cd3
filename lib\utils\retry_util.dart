import 'dart:async';

class RetryUtil {
  /// Retry an async operation with exponential backoff
  static Future<T> withRetry<T>({
    required Future<T> Function() operation,
    required int maxAttempts,
    required Duration initialDelay,
    String? operationName,
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (true) {
      try {
        attempt++;
        return await operation();
      } catch (e) {
        if (attempt >= maxAttempts) {
          throw Exception('${operationName ?? 'Operation'} failed after $maxAttempts attempts: $e');
        }
        
        // Wait with exponential backoff
        await Future.delayed(delay);
        delay *= 2; // Double the delay for next attempt
        
        print('⚠️ Attempt $attempt failed, retrying in ${delay.inSeconds}s: $e');
      }
    }
  }
}
