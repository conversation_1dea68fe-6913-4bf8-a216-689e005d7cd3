import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Add this for TextInputFormatter
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../utils/localization.dart';
import '../services/user_data_service.dart';

class CreateOrderScreen extends StatefulWidget {
  @override
  _CreateOrderScreenState createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedBeat;
  String? _selectedOutlet;
  String? _selectedOutletCode; // To store the outlet code
  String? _modeOfOrder; // Will be set to translated value in initState

  final _mondAppleQtyController = TextEditingController();
  final _mondMintQtyController = TextEditingController();
  final _mondStrawberryQtyController = TextEditingController();

  List<String> _beats = [];
  List<String> _outlets = [];
  String? _currentLocation;
  String? _dsId; // To store the user's DS ID
  bool _isSubmitting = false; // Track when the submit button is pressed

  // Available quantities
  int _availableAppleQty = 0;
  int _availableVarianceQty = 0;
  int _availableStrawberryQty = 0;

  @override
  void initState() {
    super.initState();

    // Set default mode of order using translation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _modeOfOrder = context.translate('outlet_visit');
      });
    });

    _fetchUserInfo(); // Fetch user info including DS ID
    _fetchCurrentLocation();

    // Add listeners to update totals and refresh UI for form validation
    _mondAppleQtyController.addListener(() {
      _updateTotals();
      setState(() {}); // Refresh UI to update button state
    });
    _mondMintQtyController.addListener(() {
      _updateTotals();
      setState(() {}); // Refresh UI to update button state
    });
    _mondStrawberryQtyController.addListener(() {
      _updateTotals();
      setState(() {}); // Refresh UI to update button state
    });
  }

  // Fetch user information including DS ID and available quantities
  Future<void> _fetchUserInfo() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();
        Map<String, dynamic> userData = userDoc.data() as Map<String, dynamic>;

        // Ensure quantities are stored as integers in Firestore
        // This helps fix any existing string values in the database
        bool needsUpdate = false;

        // Convert appleQty to integer if it's not already an integer
        if (userData['appleQty'] != null && !(userData['appleQty'] is int)) {
          int appleQty = int.tryParse(userData['appleQty'].toString()) ?? 0;
          userData['appleQty'] = appleQty;
          needsUpdate = true;
        }

        // Convert varianceQty to integer if it's not already an integer
        if (userData['varianceQty'] != null && !(userData['varianceQty'] is int)) {
          int varianceQty = int.tryParse(userData['varianceQty'].toString()) ?? 0;
          userData['varianceQty'] = varianceQty;
          needsUpdate = true;
        }

        // Convert strawberryQty to integer if it's not already an integer
        if (userData['strawberryQty'] != null && !(userData['strawberryQty'] is int)) {
          int strawberryQty = int.tryParse(userData['strawberryQty'].toString()) ?? 0;
          userData['strawberryQty'] = strawberryQty;
          needsUpdate = true;
        }

        // Update Firestore if any values were converted from strings to integers
        if (needsUpdate) {
          // Get the user document ID from our service
          String? userDocId = await UserDataService().getUserDocumentId();

          if (userDocId != null) {
            await FirebaseFirestore.instance.collection('users').doc(userDocId).update({
              'appleQty': int.tryParse(userData['appleQty'].toString()) ?? 0,
              'varianceQty': int.tryParse(userData['varianceQty'].toString()) ?? 0,
              'strawberryQty': int.tryParse(userData['strawberryQty'].toString()) ?? 0,
            });
          }
          print('Updated quantity fields from strings to integers');
        }

        setState(() {
          _dsId = userData['dsId'];

          // Get available quantities from user document
          // Always convert to integers regardless of the stored type
          _availableAppleQty = userData['appleQty'] != null ?
              (userData['appleQty'] is int ? userData['appleQty'] : int.tryParse(userData['appleQty'].toString()) ?? 0) : 0;
          _availableVarianceQty = userData['varianceQty'] != null ?
              (userData['varianceQty'] is int ? userData['varianceQty'] : int.tryParse(userData['varianceQty'].toString()) ?? 0) : 0;
          _availableStrawberryQty = userData['strawberryQty'] != null ?
              (userData['strawberryQty'] is int ? userData['strawberryQty'] : int.tryParse(userData['strawberryQty'].toString()) ?? 0) : 0;
        });

        print('Available quantities - Apple: $_availableAppleQty, Variance: $_availableVarianceQty, Strawberry: $_availableStrawberryQty');

        // After getting the DS ID, fetch beats assigned to this DS
        _fetchBeats();
      }
    } catch (e) {
      print('Error fetching user info: $e');
    }
  }

  @override
  void dispose() {
    // Remove all listeners and dispose controllers
    _mondAppleQtyController.dispose();
    _mondMintQtyController.dispose();
    _mondStrawberryQtyController.dispose();
    super.dispose();
  }

  Future<void> _fetchBeats() async {
    if (_dsId == null) {
      print('DS ID is not available yet');
      return;
    }

    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();
        String userName = userDoc['name'];

        // Construct the dsName (format: userName_dsId)
        String dsName = '${userName}_${_dsId}';

        // Fetch beats where dsName matches
        QuerySnapshot snapshot = await FirebaseFirestore.instance
            .collection('beats')
            .where('dsName', isEqualTo: dsName)
            .get();

        if (snapshot.docs.isEmpty) {
          // If no beats found with dsName, try fetching by dsId as fallback
          snapshot = await FirebaseFirestore.instance
              .collection('beats')
              .where('dsId', isEqualTo: _dsId)
              .get();
        }

        setState(() {
          _beats = snapshot.docs.map((doc) => doc['beatName'] as String).toList();
        });

        print('Fetched ${_beats.length} beats for DS ID: $_dsId');
      }
    } catch (e) {
      print('Error fetching beats: $e');
    }
  }

  Future<void> _fetchOutlets(String? beat) async {
    if (beat == null) return;

    try {
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('outlets')
          .where('beat', isEqualTo: beat)
          .where('dsId', isEqualTo: _dsId)
          .get();

      // Create a map of outlet names to their codes for quick lookup
      Map<String, String> outletCodeMap = {};
      for (var doc in snapshot.docs) {
        String outletName = doc['outletName'] as String;
        String outletCode = doc['outletCode'] as String;
        outletCodeMap[outletName] = outletCode;
      }

      setState(() {
        _outlets = outletCodeMap.keys.toList();
        _selectedOutlet = null;
        _selectedOutletCode = null;
      });

      // Store the map for later use
      _outletCodeMap = outletCodeMap;
    } catch (e) {
      print('Error fetching outlets: $e');
    }
  }

  // Map to store outlet names and their corresponding codes
  Map<String, String> _outletCodeMap = {};

  Future<void> _fetchCurrentLocation() async {
    if (await Permission.location.request().isGranted) {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Location services are disabled.')),
        );
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Location permissions are denied')),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Location permissions are permanently denied, we cannot request permissions.')),
        );
        return;
      }

      Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      setState(() {
        _currentLocation = '${position.latitude}, ${position.longitude}';
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Location permissions are denied')),
      );
    }
  }

  void _submit() async {
    // Prevent multiple button presses
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    if (_formKey.currentState!.validate()) {
      // Check if ordered quantities exceed available quantities
      if (!_checkQuantities()) {
        _showAvailableQuantitiesDialog();
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      if (_modeOfOrder == 'Outlet Visit' && _selectedOutlet != null) {
        bool isNearby = await _isNearbyOutlet();
        if (!isNearby) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.translate('location_error'))),
          );
          setState(() {
            _isSubmitting = false;
          });
          return;
        }
      }

      // Show confirmation dialog before proceeding
      bool confirmed = await _showConfirmationDialog();
      if (!confirmed) {
        setState(() {
          _isSubmitting = false;
        });
        return;
      }

      try {
        // Check if we have the DS ID
        if (_dsId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.translate('user_info_error'))),
          );
          setState(() {
            _isSubmitting = false;
          });
          return;
        }

        // Get the user document ID from our service
        String? userDocId = await UserDataService().getUserDocumentId();
        if (userDocId == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('User document ID not found')),
          );
          setState(() {
            _isSubmitting = false;
          });
          return;
        }

        // Calculate totals
        int totalQty = _calculateTotalQty();
        double totalTQty = _calculateTotalTQty();

        // Get ordered quantities
        int orderedAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
        int orderedVarianceQty = int.tryParse(_mondMintQtyController.text) ?? 0;
        int orderedStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

        // Calculate new inventory levels
        int newAppleQty = _availableAppleQty - orderedAppleQty;
        int newVarianceQty = _availableVarianceQty - orderedVarianceQty;
        int newStrawberryQty = _availableStrawberryQty - orderedStrawberryQty;

        // Start a batch write to ensure both operations succeed or fail together
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Add the order document
        DocumentReference orderRef = FirebaseFirestore.instance.collection('orders').doc();
        batch.set(orderRef, {
          'beat': _selectedBeat,
          'outlet': _selectedOutlet,
          'outletCode': _selectedOutletCode, // Add the outlet code
          'mondAppleQty': int.tryParse(_mondAppleQtyController.text) ?? 0, // Store as integer
          'mondMintQty': int.tryParse(_mondMintQtyController.text) ?? 0, // Store as integer
          'mondStrawberryQty': int.tryParse(_mondStrawberryQtyController.text) ?? 0, // Store as integer
          'totalQty': totalQty, // Store as integer directly
          'totalTQty': totalTQty, // Store as double directly
          'modeOfOrder': _modeOfOrder,
          'dsID': _dsId, // Use the correct DS ID from user document
          'userId': userDocId, // Store the user document ID for reference
          'checkinTimestamp': FieldValue.serverTimestamp(), // Add the current timestamp
        });

        // Update the user's inventory - store as integers, not strings
        DocumentReference userRef = FirebaseFirestore.instance.collection('users').doc(userDocId);
        batch.update(userRef, {
          'appleQty': newAppleQty,  // Store as integer, not string
          'varianceQty': newVarianceQty,  // Store as integer, not string
          'strawberryQty': newStrawberryQty,  // Store as integer, not string
        });

        // Commit the batch
        await batch.commit();

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(context.translate('order_created_success'))));
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('${context.translate('order_creation_failed')}: $e')));
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<bool> _isNearbyOutlet() async {
    try {
      DocumentSnapshot outletDoc = await FirebaseFirestore.instance
          .collection('outlets')
          .where('outletName', isEqualTo: _selectedOutlet)
          .get()
          .then((snapshot) => snapshot.docs.first);

      var outletData = outletDoc.data() as Map<String, dynamic>;
      String geoLocation = outletData['geoLocation'];
      List<String> coords = geoLocation.split(', ');
      double outletLat = double.parse(coords[0]);
      double outletLon = double.parse(coords[1]);

      Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
      double distance = Geolocator.distanceBetween(position.latitude, position.longitude, outletLat, outletLon);

      // Define the range in meters within which to consider as "nearby"
      const double range = 100.0;
      return distance <= range;
    } catch (e) {
      print('Failed to fetch outlet location: $e');
      return false;
    }
  }

  void _updateTotals() {
    setState(() {});
  }

  // Check if ordered quantities exceed available quantities
  bool _checkQuantities() {
    int orderedAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
    int orderedMintQty = int.tryParse(_mondMintQtyController.text) ?? 0;
    int orderedStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

    // Check if any ordered quantity exceeds available quantity
    if (orderedAppleQty > _availableAppleQty ||
        orderedMintQty > _availableVarianceQty ||
        orderedStrawberryQty > _availableStrawberryQty) {
      return false;
    }

    return true;
  }

  // Show popup with available quantities
  void _showAvailableQuantitiesDialog() {
    int orderedAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
    int orderedMintQty = int.tryParse(_mondMintQtyController.text) ?? 0;
    int orderedStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.translate('insufficient_quantity')),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(context.translate('quantity_error_message')),
                SizedBox(height: 16),
                _buildQuantityRow(context.translate('apple'), orderedAppleQty, _availableAppleQty),
                _buildQuantityRow(context.translate('variance'), orderedMintQty, _availableVarianceQty),
                _buildQuantityRow(context.translate('strawberry'), orderedStrawberryQty, _availableStrawberryQty),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.translate('ok')),
            ),
          ],
        );
      },
    );
  }

  // Helper method to build quantity row for the dialog
  Widget _buildQuantityRow(String itemName, int orderedQty, int availableQty) {
    bool isExceeded = orderedQty > availableQty;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(itemName),
          RichText(
            text: TextSpan(
              style: TextStyle(color: Colors.black),
              children: [
                TextSpan(text: '${context.translate('ordered')}: '),
                TextSpan(
                  text: '$orderedQty',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isExceeded ? Colors.red : Colors.black,
                  ),
                ),
                TextSpan(text: ' / ${context.translate('available')}: '),
                TextSpan(
                  text: '$availableQty',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isExceeded ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show confirmation dialog with order details
  Future<bool> _showConfirmationDialog() async {
    // Get ordered quantities
    int orderedAppleQty = int.tryParse(_mondAppleQtyController.text) ?? 0;
    int orderedVarianceQty = int.tryParse(_mondMintQtyController.text) ?? 0;
    int orderedStrawberryQty = int.tryParse(_mondStrawberryQtyController.text) ?? 0;

    // Calculate total quantity
    int totalQty = _calculateTotalQty();
    double totalTQty = _calculateTotalTQty();

    // Show confirmation dialog
    bool? result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // User must tap a button to close the dialog
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(context.translate('confirm_order')),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  context.translate('please_confirm_order_details'),
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 16),

                // Order details
                Text('${context.translate('beat')}: $_selectedBeat'),
                Text('${context.translate('outlet')}: $_selectedOutlet'),
                Text('${context.translate('mode_of_order')}: $_modeOfOrder'),

                Divider(height: 24),

                // Item quantities
                Text(
                  context.translate('ordered_quantities'),
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),

                // Only show items with quantities > 0
                if (orderedVarianceQty > 0)
                  _buildOrderConfirmationRow(context.translate('variance'), orderedVarianceQty),
                if (orderedAppleQty > 0)
                  _buildOrderConfirmationRow(context.translate('apple'), orderedAppleQty),
                if (orderedStrawberryQty > 0)
                  _buildOrderConfirmationRow(context.translate('strawberry'), orderedStrawberryQty),

                Divider(height: 24),

                // Totals
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${context.translate('total_quantity')}:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '$totalQty',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${context.translate('total_tqty')}:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${totalTQty.toStringAsFixed(2)}',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false), // Return false
              child: Text(context.translate('cancel')),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true), // Return true
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: Text(context.translate('confirm')),
            ),
          ],
        );
      },
    );

    // Return false if the user dismissed the dialog or pressed cancel
    return result ?? false;
  }

  // Helper method to build a row for the confirmation dialog
  Widget _buildOrderConfirmationRow(String itemName, int quantity) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(itemName),
          Text(
            '$quantity',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('create_order')),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedBeat,
                  hint: Text(context.translate('select_beat')),
                  onChanged: (value) {
                    setState(() {
                      _selectedBeat = value;
                      _fetchOutlets(value);
                    });
                  },
                  items: _beats.map((beat) {
                    return DropdownMenuItem(
                      value: beat,
                      child: Text(beat),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null) {
                      return context.translate('select_beat');
                    }
                    return null;
                  },
                ),
                DropdownButtonFormField<String>(
                  value: _selectedOutlet,
                  hint: Text(context.translate('select_outlet')),
                  onChanged: (value) {
                    setState(() {
                      _selectedOutlet = value;
                      // Set the outlet code based on the selected outlet name
                      _selectedOutletCode = value != null ? _outletCodeMap[value] : null;
                      print('Selected outlet: $value, Code: $_selectedOutletCode');
                    });
                  },
                  items: _outlets.map((outlet) {
                    return DropdownMenuItem(
                      value: outlet,
                      child: Text(outlet),
                    );
                  }).toList(),
                  validator: (value) {
                    if (value == null) {
                      return context.translate('select_outlet');
                    }
                    return null;
                  },
                ),
                SizedBox(height: 20),
                Text(context.translate('create_order_title'), style: TextStyle(fontSize: 20)),
                Table(
                  columnWidths: const {
                    0: FlexColumnWidth(2),    // Item column
                    1: FlexColumnWidth(1.2),  // Quantity column - slightly wider to fit 4 digits
                    2: FlexColumnWidth(0.9),  // Base column - slightly narrower
                    3: FlexColumnWidth(0.9),  // TQty column - slightly narrower
                  },
                  border: TableBorder.all(color: Colors.grey),
                  children: [
                    TableRow(
                      children: [
                        _buildTableHeaderCell(context.translate('item')),
                        _buildTableHeaderCell(context.translate('qty')),
                        _buildTableHeaderCell(context.translate('base')),
                        _buildTableHeaderCell(context.translate('tqty')),
                      ],
                    ),
                    _buildOrderRow(context.translate('variance'), _mondMintQtyController),
                    _buildOrderRow(context.translate('apple'), _mondAppleQtyController),
                    _buildOrderRow(context.translate('strawberry'), _mondStrawberryQtyController),
                    _buildTotalRow(),
                  ],
                ),
                SizedBox(height: 20),
                Text(context.translate('mode_of_order'), style: TextStyle(fontSize: 16)),
                Row(
                  children: [
                    Radio<String>(
                      value: context.translate('outlet_visit'),
                      groupValue: _modeOfOrder,
                      onChanged: (value) {
                        setState(() {
                          _modeOfOrder = value!;
                        });
                      },
                    ),
                    Text(context.translate('outlet_visit')),
                    Radio<String>(
                      value: context.translate('offline'),
                      groupValue: _modeOfOrder,
                      onChanged: (value) {
                        setState(() {
                          _modeOfOrder = value!;
                        });
                      },
                    ),
                    Text(context.translate('offline')),
                  ],
                ),
                SizedBox(height: 20),
                Center(
                  child: ElevatedButton(
                    onPressed: _isSubmitting || !_isFormValid() ? null : _submit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isSubmitting || !_isFormValid() ? Colors.grey : Theme.of(context).primaryColor,
                      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isSubmitting
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                'Processing...',
                                style: TextStyle(fontSize: 16),
                              ),
                            ],
                          )
                        : Text(
                            context.translate('submit'),
                            style: TextStyle(fontSize: 16),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  TableRow _buildOrderRow(String itemName, TextEditingController qtyController) {
    // Determine available quantity based on item name
    int availableQty = 0;

    // Use the translated item names for comparison
    String appleTranslated = context.translate('apple');
    String varianceTranslated = context.translate('variance');
    String strawberryTranslated = context.translate('strawberry');

    if (itemName == appleTranslated) {
      availableQty = _availableAppleQty;
    } else if (itemName == varianceTranslated) {
      availableQty = _availableVarianceQty;
    } else if (itemName == strawberryTranslated) {
      availableQty = _availableStrawberryQty;
    }

    return TableRow(
      children: [
        _buildTableCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(itemName),
              Text(
                '${context.translate('available')}: $availableQty',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        _buildTableCell(
          Container(
            width: 80, // Set a fixed width to accommodate at least 4 digits
            child: TextFormField(
              controller: qtyController,
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 4), // Reduce padding to maximize space
                isDense: true, // Make the input field more compact
              ),
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16), // Ensure text size is appropriate
              // Add input formatters to restrict input to numbers only
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly, // Allow only digits
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return null; // Empty is allowed, will be treated as 0
                }

                int? qty = int.tryParse(value);
                if (qty == null) {
                  return context.translate('enter_valid_number');
                }

                if (qty > availableQty) {
                  // We'll handle this in the submit method, so just return null here
                  return null;
                }
                return null;
              },
              onChanged: (value) {
                _updateTotals();
              },
            ),
          ),
        ),
        _buildTableCell(Text('0.02'), greyedOut: true), // BASE is fixed at 0.02
        _buildTableCell(Text((_calculateTQty(qtyController)).toStringAsFixed(2)), greyedOut: true),
       // _buildTableCell(
        //   TextFormField(
          //   controller: mrpController,
          //   decoration: InputDecoration(border: InputBorder.none),
          //   keyboardType: TextInputType.number,
          //   textAlign: TextAlign.center,
          //   validator: (value) {
          //     if (value == null || value.isEmpty) {
          //       return 'Enter MRP';
          //     }
          //     return null;
          //   },
          //   onChanged: (value) {
          //     _updateTotals();
          //   },
          // ),
      //  ),
      //  )
      ],
    );
  }

  TableRow _buildTotalRow() {
    return TableRow(
      children: [
        _buildTableCell(Text(context.translate('total'), style: TextStyle(fontWeight: FontWeight.bold))),
        _buildTableCell(
          Container(
            width: 80, // Match the width of the quantity input fields
            child: Text(
              _calculateTotalQty().toString(),
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ),
        _buildTableCell(Text('')),
        _buildTableCell(Text(_calculateTotalTQty().toStringAsFixed(2), style: TextStyle(fontWeight: FontWeight.bold))),
     //   _buildTableCell(SizedBox.shrink()), // MRP column is empty in total
      ],
    );
  }

  Widget _buildTableHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: Colors.grey[300],
      child: Center(child: Text(text)),
    );
  }

  Widget _buildTableCell(Widget child, {bool greyedOut = false}) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      color: greyedOut ? Colors.grey[300] : null,
      child: Center(child: child),
    );
  }

  int _calculateTotalQty() {
    return (int.tryParse(_mondMintQtyController.text) ?? 0) +
        (int.tryParse(_mondAppleQtyController.text) ?? 0) +
        (int.tryParse(_mondStrawberryQtyController.text) ?? 0);
  }

  double _calculateTotalTQty() {
    return (_calculateTQty(_mondMintQtyController) +
        _calculateTQty(_mondAppleQtyController) +
        _calculateTQty(_mondStrawberryQtyController));
  }

  double _calculateTQty(TextEditingController qtyController) {
    return (double.tryParse(qtyController.text) ?? 0) * 0.02;
  }

  // Check if all required fields are filled
  bool _isFormValid() {
    // Check if beat and outlet are selected
    if (_selectedBeat == null || _selectedOutlet == null) {
      return false;
    }

    // Check if at least one product has a quantity greater than 0
    int totalQty = _calculateTotalQty();
    if (totalQty <= 0) {
      return false;
    }

    return true;
  }
}
