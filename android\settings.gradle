// This file configures the Gradle build system for Flutter projects
pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    plugins {
        id 'com.android.application' version '8.1.0'
        id 'com.android.library' version '8.1.0'
        id 'org.jetbrains.kotlin.android' version '1.9.0'
    }
}

// Define properties first
def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
def properties = new Properties()

assert localPropertiesFile.exists()
localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

def flutterSdkPath = properties.getProperty("flutter.sdk")
assert flutterSdkPath != null, "flutter.sdk not set in local.properties"

// Note: We're using the apply method for the Flutter app plugin loader because the declarative
// plugins block with 'dev.flutter.flutter-gradle-plugin' is not available in this Flutter version.
// This will be updated in future Flutter versions.
apply from: "$flutterSdkPath/packages/flutter_tools/gradle/app_plugin_loader.gradle"

// Include the app project
include ':app'
