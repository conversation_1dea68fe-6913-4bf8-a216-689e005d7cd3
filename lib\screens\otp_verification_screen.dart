import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../theme/app_theme.dart';
import '../services/fcm_service.dart';

import '../services/user_data_service.dart';

class OtpVerificationScreen extends StatefulWidget {
  final String mobileNumber;
  final String verificationId;
  final Map<String, dynamic> userData;

  const OtpVerificationScreen({
    Key? key,
    required this.mobileNumber,
    required this.verificationId,
    required this.userData,
  }) : super(key: key);

  @override
  _OtpVerificationScreenState createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final TextEditingController _otpController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isVerifying = false;
  String? _deviceId;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Get device ID from userData
    _deviceId = widget.userData['deviceId'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text('Verification'),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 20),
                  // OTP Icon
                  Center(
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.sms_outlined,
                        size: 60,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 24),
                  // Title
                  Center(
                    child: Text(
                      'Verification Code',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 12),
                  // Description
                  Center(
                    child: Text(
                      'We have sent a verification code to\n+91 ${widget.mobileNumber}',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 8),
                  Center(
                    child: Text(
                      'Please enter the code manually',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                  SizedBox(height: 40),
                  // OTP Input Field
                  TextFormField(
                    controller: _otpController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 24, letterSpacing: 8),
                    maxLength: 6,
                    decoration: InputDecoration(
                      hintText: '------',
                      counterText: '',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                      ),
                      enabled: !_isVerifying,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter the verification code';
                      }
                      if (value.length != 6) {
                        return 'Verification code must be 6 digits';
                      }
                      return null;
                    },
                  ),
                  SizedBox(height: 30),
                  // Verify Button
                  ElevatedButton(
                    onPressed: _isVerifying ? null : _verifyOtp,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isVerifying ? Colors.grey : AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: _isVerifying
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                              SizedBox(width: 12),
                              Text(
                                'Verifying...',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            'Verify',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 1.2,
                            ),
                          ),
                  ),
                  SizedBox(height: 20),
                  // Resend Code
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Didn't receive the code? ",
                        style: TextStyle(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      TextButton(
                        onPressed: _isVerifying ? null : () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          'Resend',
                          style: TextStyle(
                            color: _isVerifying ? Colors.grey : AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _verifyOtp() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isVerifying = true;
      });

      try {
        // Manual OTP verification (auto-retrieval disabled by design)
        print('📱 Manual OTP verification - auto-retrieval disabled');

        final code = _otpController.text.trim();

        // Create the credential
        PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: widget.verificationId,
          smsCode: code,
        );

        // Sign in with the credential
        await _signInWithPhoneCredential(credential);
      } catch (e) {
        print('Error verifying code: $e');

        setState(() {
          _isVerifying = false;
        });

        // Show appropriate error message
        String errorMessage = 'Invalid verification code. Please try again.';
        if (e.toString().contains('network')) {
          errorMessage = 'Network error. Please check your internet connection.';
        }

        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      }
    }
  }

  // Sign in with phone credential
  Future<void> _signInWithPhoneCredential(PhoneAuthCredential credential) async {
    try {
      print('Attempting to sign in with phone credential');
      // Sign in with the credential
      UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Get the user ID
      String userId = userCredential.user!.uid;
      print('Successfully signed in with phone credential. User ID: $userId');

      // Check if the user document exists with this UID
      print('Fetching user document from Firestore');
      DocumentSnapshot userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      print('User document exists: ${userDoc.exists}');
      if (userDoc.exists) {
        print('User document data: ${userDoc.data()}');
      }

      // Instead of creating a new document, we'll find the existing document by mobile number
      // and update it with the Firebase Auth UID
      String mobileNumber = widget.mobileNumber;

      // Check if there's an existing document with this mobile number
      QuerySnapshot existingUserQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('mobileNumber', isEqualTo: mobileNumber)
          .get();

      if (existingUserQuery.docs.isNotEmpty) {
        // Get the existing document
        DocumentSnapshot existingUserDoc = existingUserQuery.docs.first;
        String existingDocId = existingUserDoc.id;

        // If the existing document has a different ID than the Firebase Auth UID
        if (existingDocId != userId) {
          print('Found existing user document with ID: $existingDocId');

          // Get the data from the existing document
          Map<String, dynamic> existingData = existingUserDoc.data() as Map<String, dynamic>;

          // Update the authStatus field
          existingData['authStatus'] = 'done';
          existingData['firebaseAuthUid'] = userId; // Store the Firebase Auth UID for reference

          // Update the existing document
          await FirebaseFirestore.instance
              .collection('users')
              .doc(existingDocId)
              .update(existingData);

          print('Updated existing user document with Firebase Auth UID');

          // Now we need to use the existing document ID for all operations
          userId = existingDocId;

          // Store the document ID in our service for future use
          await UserDataService().setUserDocumentId(userId);

          // Fetch the updated document
          userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .get();
        } else {
          // The document already exists with the Firebase Auth UID, just update authStatus
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({'authStatus': 'done'});
        }
      } else if (!userDoc.exists) {
        // This is a fallback case - if somehow we can't find the document by mobile number
        // but we also don't have a document with the Firebase Auth UID
        print('Warning: Could not find user document by mobile number or Firebase Auth UID');

        // Create a new document with the Auth UID as a last resort
        Map<String, dynamic> userData = widget.userData;
        userData['authStatus'] = 'done';

        await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .set(userData);

        print('Created new user document with Firebase Auth UID as fallback');
      }

      // Safely access fields with null checks
      Map<String, dynamic>? data = userDoc.data() as Map<String, dynamic>?;
      String? currentDeviceId = data?['deviceId'];

      // We've already checked KYC status before sending OTP, so we know it's approved
      // Just check device ID
      if (currentDeviceId == null || currentDeviceId == _deviceId) {
        // Update Firestore with the current device ID
        try {
          print('Updating device ID in Firestore to: $_deviceId');
          await FirebaseFirestore.instance
              .collection('users')
              .doc(userId)
              .update({'deviceId': _deviceId});
          print('Successfully updated device ID in Firestore');
        } catch (updateError) {
          print('Error updating device ID: $updateError');
          // Continue anyway, this is not critical for login
        }

        // Register FCM token for the user
        try {
          await FCMService().handleUserLogin(userCredential.user!);
          print('FCM token registered for user: $userId');
        } catch (fcmError) {
          print('Error registering FCM token: $fcmError');
          // Continue anyway, this is not critical
        }

        // Navigate to dashboard
        Navigator.pushReplacementNamed(context, '/dashboard');
      } else {
        // User is already logged in on another device
        await FirebaseAuth.instance.signOut();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('You are already logged in on another device. Please log out from that device first.'),
        ));
      }
    } catch (e) {
      print('Error signing in with phone credential: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Error signing in: $e'),
      ));
      setState(() {
        _isVerifying = false;
      });
    }
  }
}
