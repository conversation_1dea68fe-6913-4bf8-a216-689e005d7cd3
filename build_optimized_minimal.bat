@echo off
echo Building optimized minimal APK...

REM Clean the project
flutter clean

REM Get dependencies
flutter pub get

REM Run the size reduction script
call reduce_apk_size.bat

REM Build APK with minimal options and size optimizations
echo Building optimized APK...
flutter build apk --release --target-platform android-arm64

echo Build completed!
echo APK is available in build\app\outputs\apk\release\quickk-ds-panel.apk
