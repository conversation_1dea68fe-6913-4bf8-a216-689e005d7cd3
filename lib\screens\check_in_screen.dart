// lib/screens/check_in_screen.dart
import 'package:flutter/material.dart';

class CheckInScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Check In'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Start your day !!!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: () {
                // Add your check-in logic here
              },
              icon: Icon(Icons.location_on),
              label: Text('CHECK IN'),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white, backgroundColor: Colors.blue, // Text color
              ),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.timer, size: 40),
                <PERSON><PERSON><PERSON><PERSON>(width: 10),
                Column(
                  children: [
                    Text(
                      'Timer',
                      style: TextStyle(fontSize: 16),
                    ),
                    Text(
                      '00:00',
                      style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 40),
            Text(
              'Business Dashboard',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    Text('Outlet Visited'),
                    Container(
                      width: 50,
                      height: 30,
                      color: Colors.grey[200],
                      child: Center(child: Text('—')),
                    ),
                  ],
                ),
                Column(
                  children: [
                    Text('Bill Cut'),
                    Container(
                      width: 50,
                      height: 30,
                      color: Colors.grey[200],
                      child: Center(child: Text('—')),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}