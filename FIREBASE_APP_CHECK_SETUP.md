# Firebase App Check Setup Guide

## Current Issue
The app is showing: "This app is not authorized to use Firebase Authentication. Please verify that correct package name, SHA-1 and SHA-256 are configured in the Firebase console. [invalid PlayIntegrity token, does not pass basic integrity]"

## Root Cause
The error occurs because:
1. Firebase App Check is now properly enabled and working
2. The debug token needs to be registered in Firebase Console
3. SHA-1 and SHA-256 fingerprints may need to be added to Firebase Console

## Step-by-Step Solution

### Step 1: Get SHA Fingerprints
1. Run the `get_sha_fingerprints.bat` file in the project root
2. Copy the SHA-1 and SHA-256 fingerprints from the debug keystore output

### Step 2: Add SHA Fingerprints to Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project "ds-panel-quickk"
3. Go to Project Settings (gear icon)
4. Scroll down to "Your apps" section
5. Find your Android app (com.example.ds_panel)
6. Click "Add fingerprint"
7. Paste the SHA-1 fingerprint and click "Add"
8. Click "Add fingerprint" again
9. Paste the SHA-256 fingerprint and click "Add"
10. Click "Save"

### Step 3: Get Debug Token from App
1. Run the app and check the console logs
2. Look for lines like:
   ```
   FIREBASE APP CHECK DEBUG TOKEN:
   XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
   ```
3. Copy this debug token

### Step 4: Register Debug Token in Firebase Console
1. Go to Firebase Console > Project Settings > App Check
2. In the "Apps" section, find your Android app
3. Click "Add debug token"
4. Paste the debug token from Step 3
5. Click "Add"

### Step 5: Configure App Check Enforcement
1. In Firebase Console > Project Settings > App Check
2. For **Firebase Authentication**: Set to "Enforce"
3. For **Cloud Firestore**: Set to "Enforce"
4. Click "Save"

### Step 6: Test the App
1. Clean and rebuild the project:
   ```bash
   flutter clean
   cd android && ./gradlew clean && cd ..
   flutter pub get
   flutter run
   ```
2. Try authentication
3. Check Firebase Console App Check section to see verified requests

## Verification Checklist

- [ ] SHA-1 fingerprint added to Firebase Console
- [ ] SHA-256 fingerprint added to Firebase Console
- [ ] Debug token registered in Firebase Console App Check
- [ ] App Check enforcement enabled for Authentication and Firestore
- [ ] App rebuilt after changes
- [ ] Authentication working without errors
- [ ] Requests showing as "Verified" in Firebase Console

## Common Issues and Solutions

### Issue: "Invalid PlayIntegrity token"
**Solution**: This is expected for debug builds. Make sure you're using the debug provider and have registered the debug token.

### Issue: "App not authorized"
**Solution**: Add SHA-1 and SHA-256 fingerprints to Firebase Console.

### Issue: Still getting unverified requests
**Solution**: 
1. Make sure the debug token is correctly registered
2. Restart the app after registering the token
3. Check that App Check enforcement is enabled

### Issue: Authentication fails completely
**Solution**:
1. Temporarily disable App Check enforcement
2. Test authentication
3. Re-enable enforcement after confirming setup

## Debug Commands

Get SHA fingerprints:
```bash
cd android
./gradlew signingReport
```

Check Firebase App Check status in app logs:
```bash
flutter run
# Look for "FirebaseAppCheck" logs
```

## Contact
If issues persist, check the Firebase Console App Check dashboard for detailed error information.
