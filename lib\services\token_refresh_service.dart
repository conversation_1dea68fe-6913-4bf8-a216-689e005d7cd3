import 'dart:async';
import 'package:flutter/foundation.dart';
import '../config/app_check_config.dart';
import '../utils/retry_util.dart';
import 'app_check_service.dart';

class TokenRefreshService {
  static final TokenRefreshService _instance = TokenRefreshService._internal();
  factory TokenRefreshService() => _instance;
  TokenRefreshService._internal();

  Timer? _refreshTimer;
  AppCheckService? _appCheckService;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    _appCheckService = await AppCheckService.initialize();
    _startRefreshTimer();
    _isInitialized = true;
  }

  void _startRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      AppCheckConfig.tokenRefreshInterval,
      (_) => _refreshToken(),
    );
  }

  Future<void> _refreshToken() async {
    if (_appCheckService == null) return;

    try {
      await RetryUtil.withRetry(
        operation: () => _appCheckService!.validateAndRefreshToken(force: true),
        maxAttempts: AppCheckConfig.maxRetryAttempts,
        initialDelay: AppCheckConfig.retryDelay,
        operationName: 'Token refresh',
      );
    } catch (e) {
      debugPrint('❌ Failed to refresh token after all retries: $e');
      // Don't throw, just log the error as this is a background operation
    }
  }

  void dispose() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    _isInitialized = false;
  }
}
