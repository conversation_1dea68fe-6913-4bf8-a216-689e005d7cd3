import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:badges/badges.dart' as badges;
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../services/notification_service.dart';
import '../services/fcm_service.dart';
import '../services/language_service.dart';
import '../services/user_data_service.dart';
import '../widgets/language_switcher.dart';
import '../utils/localization.dart';

class DashboardScreen extends StatefulWidget {
  @override
  _DashboardScreenState createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with WidgetsBindingObserver {
  bool isCheckedIn = false;
  // Timer declarations moved below
  int seconds = 0;
  DateTime? _checkinTime; // To store the check-in time
  // Removed outlet-related variables
  String? _geoLocation;
  String? _checkinDocumentId; // To store the document ID for updating later
  String? _dsID; // To store the dsID of the user
  String? _userName; // To store the user's name
  DateTime? _lastBackPressed; // For double back press to exit

  // Keys for SharedPreferences
  static const String KEY_IS_CHECKED_IN = 'is_checked_in';
  static const String KEY_CHECKIN_TIME = 'checkin_time';
  static const String KEY_CHECKIN_DOC_ID = 'checkin_doc_id';

  // Get user-specific key by appending user ID
  String _getUserSpecificKey(String baseKey) {
    final User? user = FirebaseAuth.instance.currentUser;
    return user != null ? '${baseKey}_${user.uid}' : baseKey;
  }

  // Notification service
  final NotificationService _notificationService = NotificationService();
  int _unreadNotificationCount = 0;
  StreamSubscription? _notificationSubscription;

  @override
  void initState() {
    super.initState();
    // Register for lifecycle events
    WidgetsBinding.instance.addObserver(this);

    _fetchCurrentLocation();
    // First fetch user info, then load saved state
    _fetchUserInfo().then((_) {
      _loadSavedState(); // Load saved check-in state after user info is fetched

      // After user info is fetched, check for notifications and create a test one if needed
      _checkAndCreateNotification();
    });

    // Initialize unread count
    _unreadNotificationCount = _notificationService.unreadCount;

    // Listen to unread notification count
    _notificationSubscription = _notificationService.unreadCountStream.listen((count) {
      print('Notification count updated: $count');
      if (mounted) {
        setState(() {
          _unreadNotificationCount = count;
        });
      }
    });

    // Run notification cleanup on app start
    _notificationService.cleanupOldNotifications();
  }

  // Check for notifications and create a test one if needed
  Future<void> _checkAndCreateNotification() async {
    try {
      // Get the user document ID
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        print('Checking notifications for user: $userDocId');

        // Check if there are any notifications for this user
        QuerySnapshot notificationsSnapshot = await FirebaseFirestore.instance
            .collection('ds_notifications')
            .where('userId', isEqualTo: userDocId)
            .limit(1)
            .get();

        print('Found ${notificationsSnapshot.docs.length} notifications for user: $userDocId');

        // If no notifications exist, create a welcome notification
        if (notificationsSnapshot.docs.isEmpty) {
          print('Creating welcome notification for user: $userDocId');

          // Create a welcome notification
          await FirebaseFirestore.instance.collection('ds_notifications').add({
            'userId': userDocId,
            'title': 'Welcome to DS Panel',
            'body': 'Thank you for using the DS Panel app. You will receive important notifications here.',
            'isRead': false,
            'createdAt': FieldValue.serverTimestamp(),
          });

          print('Welcome notification created successfully');
        }

        // Force update the notification count
        await _notificationService.updateUnreadCount();
      }
    } catch (e) {
      print('Error checking/creating notifications: $e');
    }
  }

  // Create a test notification directly in Firestore
  Future<void> _createTestNotification() async {
    try {
      // Get the user document ID
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        print('Creating test notification for user: $userDocId');

        // Create a timestamp for the notification
        String timestamp = DateTime.now().toString();

        // Create a test notification
        await FirebaseFirestore.instance.collection('ds_notifications').add({
          'userId': userDocId,
          'title': 'Test Notification',
          'body': 'This is a test notification created at $timestamp',
          'isRead': false,
          'createdAt': FieldValue.serverTimestamp(),
        });

        print('Test notification created successfully');

        // Force update the notification count
        await _notificationService.updateUnreadCount();
      }
    } catch (e) {
      print('Error creating test notification: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Handle app lifecycle changes
    if (state == AppLifecycleState.detached || state == AppLifecycleState.hidden) {
      // App is being terminated or hidden
      _handleAppTermination();
    }
  }

  // Handle app termination by performing auto-checkout if needed
  Future<void> _handleAppTermination() async {
    if (isCheckedIn && _checkinDocumentId != null) {
      try {
        // First get the check-in document to calculate the actual elapsed time
        DocumentSnapshot checkinDoc = await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).get();

        // Get the check-in timestamp
        Timestamp? checkinTimestamp = checkinDoc['checkinTimestamp'] as Timestamp?;

        // Calculate the elapsed time in seconds
        int elapsedSeconds = 0;
        String formattedElapsedTime = '00:00:00';

        if (checkinTimestamp != null) {
          DateTime checkinTime = checkinTimestamp.toDate();
          DateTime now = DateTime.now();
          Duration elapsed = now.difference(checkinTime);
          elapsedSeconds = elapsed.inSeconds;
          formattedElapsedTime = _formatTime(elapsedSeconds);

          // Synchronize the seconds variable with the actual elapsed time
          // This ensures timerValue and actualElapsedTime are the same
          seconds = elapsedSeconds;
        }

        // Update the existing check-in document with checkout timestamp
        await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).update({
          'checkoutTimestamp': FieldValue.serverTimestamp(),
          'autoCheckout': true,  // Flag to indicate this was an automatic checkout
          'checkoutReason': 'app_terminated',
          'timerValue': _formatTime(seconds), // Now this will match the actual elapsed time
          'actualElapsedTime': formattedElapsedTime, // The actual elapsed time
          'elapsedSeconds': elapsedSeconds, // Store the elapsed seconds
        });
        print('Auto checkout completed due to app termination');
      } catch (e) {
        print('Error performing auto checkout during app termination: $e');
      }
    }
  }

  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    // Perform auto-checkout if still checked in
    if (isCheckedIn && _checkinDocumentId != null) {
      _handleAppTermination();
    }

    _notificationSubscription?.cancel();
    timer?.cancel();
    super.dispose();
  }

  Future<void> _fetchUserInfo() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();

        if (mounted) {
          setState(() {
            _dsID = userDoc['dsId'];
            _userName = userDoc['name'];
          });
        }
      } else {
        print('User document ID not found');
      }
    } catch (e) {
      print('Error fetching user info: $e');
    }
  }

  void _toggleCheckIn() async {
    if (isCheckedIn) {
      _stopTimer();
      await _makeFirestoreEntry(isCheckIn: false);
      await _saveState(isCheckedIn: false);
      setState(() {
        isCheckedIn = false;
        seconds = 0;
      });
    } else {
      setState(() {
        isCheckedIn = true;
      });
      _startTimer();
      await _makeFirestoreEntry(isCheckIn: true);
      await _saveState(isCheckedIn: true);
    }
  }

  // Load saved check-in state from SharedPreferences
  Future<void> _loadSavedState() async {
    try {
      // Get current user ID
      final User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('No user logged in, cannot load check-in state');
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final savedIsCheckedIn = prefs.getBool(_getUserSpecificKey(KEY_IS_CHECKED_IN)) ?? false;
      final savedCheckinTimeString = prefs.getString(_getUserSpecificKey(KEY_CHECKIN_TIME));
      final savedCheckinDocId = prefs.getString(_getUserSpecificKey(KEY_CHECKIN_DOC_ID));

      if (savedIsCheckedIn && savedCheckinDocId != null) {
        _checkinDocumentId = savedCheckinDocId;

        // Get the actual check-in timestamp from Firestore
        try {
          DocumentSnapshot checkinDoc = await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).get();

          if (checkinDoc.exists) {
            Timestamp? checkinTimestamp = checkinDoc['checkinTimestamp'] as Timestamp?;

            if (checkinTimestamp != null) {
              DateTime checkinTime = checkinTimestamp.toDate();
              _checkinTime = checkinTime;

              // Calculate elapsed seconds based on the Firestore timestamp
              final now = DateTime.now();
              final difference = now.difference(checkinTime);
              seconds = difference.inSeconds;

              if (mounted) {
                setState(() {
                  isCheckedIn = true;
                  // Start the timer to continue counting
                  _startTimer();
                });
              }
            } else {
              // Fallback to saved time if Firestore timestamp is null
              if (savedCheckinTimeString != null) {
                final savedCheckinTime = DateTime.parse(savedCheckinTimeString);
                _checkinTime = savedCheckinTime;

                // Calculate elapsed seconds
                final now = DateTime.now();
                final difference = now.difference(savedCheckinTime);
                seconds = difference.inSeconds;

                if (mounted) {
                  setState(() {
                    isCheckedIn = true;
                    // Start the timer to continue counting
                    _startTimer();
                  });
                }
              }
            }
          } else {
            // Document doesn't exist, clear the saved state
            await _saveState(isCheckedIn: false);
          }
        } catch (firestoreError) {
          print('Error fetching check-in document: $firestoreError');

          // Fallback to saved time if Firestore fetch fails
          if (savedCheckinTimeString != null) {
            final savedCheckinTime = DateTime.parse(savedCheckinTimeString);
            _checkinTime = savedCheckinTime;

            // Calculate elapsed seconds
            final now = DateTime.now();
            final difference = now.difference(savedCheckinTime);
            seconds = difference.inSeconds;

            if (mounted) {
              setState(() {
                isCheckedIn = true;
                // Start the timer to continue counting
                _startTimer();
              });
            }
          }
        }
      }
    } catch (e) {
      print('Error loading saved state: $e');
    }
  }

  // Save check-in state to SharedPreferences
  Future<void> _saveState({required bool isCheckedIn}) async {
    try {
      // Get current user ID
      final User? user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        print('No user logged in, cannot save check-in state');
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_getUserSpecificKey(KEY_IS_CHECKED_IN), isCheckedIn);

      if (isCheckedIn) {
        _checkinTime = DateTime.now();
        await prefs.setString(_getUserSpecificKey(KEY_CHECKIN_TIME), _checkinTime!.toIso8601String());
        if (_checkinDocumentId != null) {
          await prefs.setString(_getUserSpecificKey(KEY_CHECKIN_DOC_ID), _checkinDocumentId!);
        }
      } else {
        // Clear check-in time when checking out
        await prefs.remove(_getUserSpecificKey(KEY_CHECKIN_TIME));
        await prefs.remove(_getUserSpecificKey(KEY_CHECKIN_DOC_ID));
      }
    } catch (e) {
      print('Error saving state: $e');
    }
  }

  void _startTimer() {
    // Regular timer to increment seconds
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          seconds++;
        });
      } else {
        // Cancel the timer if the widget is no longer mounted
        timer.cancel();
      }
    });

    // Add a periodic sync every 5 minutes to prevent drift
    syncTimer = Timer.periodic(Duration(minutes: 5), (t) async {
      if (!mounted || !isCheckedIn || _checkinDocumentId == null) {
        t.cancel();
        return;
      }

      try {
        // Get the check-in document to calculate the actual elapsed time
        DocumentSnapshot checkinDoc = await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).get();

        // Get the check-in timestamp
        Timestamp? checkinTimestamp = checkinDoc['checkinTimestamp'] as Timestamp?;

        if (checkinTimestamp != null) {
          DateTime checkinTime = checkinTimestamp.toDate();
          DateTime now = DateTime.now();
          Duration elapsed = now.difference(checkinTime);
          int elapsedSeconds = elapsed.inSeconds;

          // Only update if there's a significant difference (more than 5 seconds)
          if (mounted && (elapsedSeconds - seconds).abs() > 5) {
            print('Timer drift detected. Syncing timer: app=$seconds, actual=$elapsedSeconds');
            setState(() {
              seconds = elapsedSeconds;
            });
          }
        }
      } catch (e) {
        print('Error syncing timer: $e');
      }
    });
  }

  // Store both timers
  Timer? timer;
  Timer? syncTimer;

  void _stopTimer() {
    timer?.cancel();
    syncTimer?.cancel();
  }

  Future<void> _makeFirestoreEntry({required bool isCheckIn}) async {
    try {
      if (isCheckIn) {
        // Create a new document for check-in
        DocumentReference docRef = await FirebaseFirestore.instance.collection('checkins').add({
          'timerValue': _formatTime(seconds),
          'checkinTimestamp': FieldValue.serverTimestamp(),
          'dsID': _dsID, // Include the dsID
          'geoLocation': _geoLocation, // Include the location
        });
        _checkinDocumentId = docRef.id; // Store the document ID
      } else {
        // Update the existing document for check-out
        if (_checkinDocumentId != null) {
          // First get the check-in document to calculate the actual elapsed time
          DocumentSnapshot checkinDoc = await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).get();

          // Get the check-in timestamp
          Timestamp? checkinTimestamp = checkinDoc['checkinTimestamp'] as Timestamp?;

          // Calculate the elapsed time in seconds
          int elapsedSeconds = 0;
          String formattedElapsedTime = '00:00:00';

          if (checkinTimestamp != null) {
            DateTime checkinTime = checkinTimestamp.toDate();
            DateTime now = DateTime.now();
            Duration elapsed = now.difference(checkinTime);
            elapsedSeconds = elapsed.inSeconds;
            formattedElapsedTime = _formatTime(elapsedSeconds);

            // Update the seconds variable to show the correct time in the UI
            setState(() {
              seconds = elapsedSeconds;
            });
          }

          // Update the document with the checkout timestamp and the calculated elapsed time
          await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).update({
            'checkoutTimestamp': FieldValue.serverTimestamp(),
            'timerValue': _formatTime(seconds), // Add the timer value for consistency
            'actualElapsedTime': formattedElapsedTime, // Store the actual elapsed time
            'elapsedSeconds': elapsedSeconds, // Store the elapsed seconds for potential future calculations
          });
        }
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(isCheckIn ? 'Check-in entry added successfully' : 'Check-out entry added successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to add entry: $e')),
      );
    }
  }

  String _formatTime(int seconds) {
    final int minutes = seconds ~/ 60;
    final int hours = minutes ~/ 60;
    final int remainingSeconds = seconds % 60;
    final int remainingMinutes = minutes % 60;
    return '${_twoDigits(hours)}:${_twoDigits(remainingMinutes)}:${_twoDigits(remainingSeconds)}';
  }

  String _twoDigits(int n) {
    return n.toString().padLeft(2, '0');
  }

  Widget _buildQuickActionButton(String text, IconData icon, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 24, color: AppTheme.primaryColor),
            SizedBox(height: 4),
            Text(
              text,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }



  Future<void> _fetchCurrentLocation() async {
    try {
      // First check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.translate('location_services_disabled'))),
        );
        return;
      }

      // Check current permission status without requesting
      LocationPermission permission = await Geolocator.checkPermission();

      // If denied, request permission through Geolocator (not Permission handler)
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(context.translate('location_permissions_denied'))),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.translate('location_permissions_permanently_denied'))),
        );
        return;
      }

      // Get current position if permission is granted
      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
        if (mounted) {
          setState(() {
            _geoLocation = '${position.latitude}, ${position.longitude}';
          });
        }
      }
    } catch (e) {
      print('Error fetching location: $e');
      // Don't show error to user unless it's critical
    }
  }

  // Removed _fetchNearbyOutlets method

  Future<void> _signOut() async {
    try {
      User? user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // Handle check-out if user is checked in
        if (isCheckedIn && _checkinDocumentId != null) {
          try {
            // First get the check-in document to calculate the actual elapsed time
            DocumentSnapshot checkinDoc = await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).get();

            // Get the check-in timestamp
            Timestamp? checkinTimestamp = checkinDoc['checkinTimestamp'] as Timestamp?;

            // Calculate the elapsed time in seconds
            int elapsedSeconds = 0;
            String formattedElapsedTime = '00:00:00';

            if (checkinTimestamp != null) {
              DateTime checkinTime = checkinTimestamp.toDate();
              DateTime now = DateTime.now();
              Duration elapsed = now.difference(checkinTime);
              elapsedSeconds = elapsed.inSeconds;
              formattedElapsedTime = _formatTime(elapsedSeconds);

              // Synchronize the seconds variable with the actual elapsed time
              // This ensures timerValue and actualElapsedTime are the same
              seconds = elapsedSeconds;
            }

            // Update the existing check-in document with checkout timestamp
            await FirebaseFirestore.instance.collection('checkins').doc(_checkinDocumentId).update({
              'checkoutTimestamp': FieldValue.serverTimestamp(),
              'autoCheckout': true,  // Flag to indicate this was an automatic checkout due to logout
              'checkoutReason': 'user_logout',
              'timerValue': _formatTime(seconds), // Now this will match the actual elapsed time
              'actualElapsedTime': formattedElapsedTime, // The actual elapsed time
              'elapsedSeconds': elapsedSeconds, // Store the elapsed seconds
            });
            print('Auto checkout completed due to logout');
          } catch (checkoutError) {
            print('Error performing auto checkout: $checkoutError');
            // Continue anyway, this is not critical
          }
        }

        // Clear device ID
        String? userDocId = await UserDataService().getUserDocumentId();
        if (userDocId != null) {
          await FirebaseFirestore.instance.collection('users').doc(userDocId).update({'deviceId': null});
        } else {
          print('User document ID not found when clearing device ID');
        }

        // Handle FCM token cleanup
        try {
          await FCMService().handleUserLogout();
          print('FCM token cleanup completed');
        } catch (fcmError) {
          print('Error cleaning up FCM token: $fcmError');
          // Continue anyway, this is not critical
        }

        // Clear check-in state for this user
        try {
          // Stop the timer if it's running
          if (isCheckedIn) {
            _stopTimer();
            setState(() {
              isCheckedIn = false;
              seconds = 0;
            });
          }

          // Clear SharedPreferences for this user
          final prefs = await SharedPreferences.getInstance();
          await prefs.remove(_getUserSpecificKey(KEY_IS_CHECKED_IN));
          await prefs.remove(_getUserSpecificKey(KEY_CHECKIN_TIME));
          await prefs.remove(_getUserSpecificKey(KEY_CHECKIN_DOC_ID));
          print('Cleared check-in state for user: ${user.uid}');
        } catch (stateError) {
          print('Error clearing check-in state: $stateError');
          // Continue anyway, this is not critical
        }
      }
      // Clear the user document ID from our service
      await UserDataService().clearUserDocumentId();

      // Sign out from Firebase Auth
      await FirebaseAuth.instance.signOut();

      // Navigate to the welcome screen
      Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text('Failed to sign out: $e'),
      ));
    }
  }

  // Future<bool> _onWillPop() async {
  //   if (_lastBackPressed == null ||
  //       DateTime.now().difference(_lastBackPressed!) > Duration(seconds: 2)) {
  //     _lastBackPressed = DateTime.now();
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(content: Text('Press back again to exit')),
  //     );
  //     return Future.value(false);
  //   }
  //   return Future.value(true);
  // }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        // If didPop is true, the pop already happened, so we don't need to handle it
        if (didPop) return;

        // Handle double back press to exit the app
        if (_lastBackPressed == null ||
            DateTime.now().difference(_lastBackPressed!) > Duration(seconds: 2)) {
          _lastBackPressed = DateTime.now();
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(context.translate('press_back_again_to_exit')),
            duration: Duration(seconds: 2),
          ));
        } else {
          // Exits the app directly on the second back press
          exit(0);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(context.translate('dashboard'), style: TextStyle(fontWeight: FontWeight.bold)),
          elevation: 0, // Remove shadow for a cleaner look
          actions: [
            // Language switcher
            LanguageSwitcher(),
            // Notification icon with badge
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Stack(
                children: [
                  IconButton(
                    icon: Icon(Icons.notifications_outlined, size: 28),
                    onPressed: () {
                      Navigator.pushNamed(context, '/notifications');
                    },
                  ),
                  if (_unreadNotificationCount > 0)
                    Positioned(
                      right: 5,
                      top: 5,
                      child: Container(
                        padding: EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: BoxConstraints(
                          minWidth: 18,
                          minHeight: 18,
                        ),
                        child: Center(
                          child: Text(
                            _unreadNotificationCount.toString(),
                            style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        // Use a Scaffold with a bottom navigation bar approach
        body: SafeArea(
          child: Stack(
            children: [
              // Main content - scrollable
              Positioned.fill(
                bottom: 80, // Reserve space for the simplified bottom panel
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Enhanced Welcome Card with User Name
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppTheme.primaryColor,
                              AppTheme.primaryColor.withOpacity(0.8),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.primaryColor.withOpacity(0.3),
                              blurRadius: 10,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Responsive layout for header
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // User info section with profile button
                                  Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () => Navigator.pushNamed(context, '/profile'),
                                        child: Container(
                                          padding: EdgeInsets.all(2),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Stack(
                                            children: [
                                              CircleAvatar(
                                                backgroundColor: Colors.white,
                                                radius: 26,
                                                child: Icon(Icons.person, color: AppTheme.primaryColor, size: 30),
                                              ),
                                              Positioned(
                                                right: 0,
                                                bottom: 0,
                                                child: Container(
                                                  padding: EdgeInsets.all(4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    shape: BoxShape.circle,
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black.withOpacity(0.2),
                                                        blurRadius: 2,
                                                        offset: Offset(0, 1),
                                                      ),
                                                    ],
                                                  ),
                                                  child: Icon(Icons.edit, size: 12, color: AppTheme.primaryColor),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              context.translate('welcome') + ',',
                                              style: TextStyle(fontSize: 16, color: Colors.white.withOpacity(0.9), fontWeight: FontWeight.w500),
                                            ),
                                            Text(
                                              _userName ?? 'DS Partner',
                                              style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: Colors.white),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),

                                  // Date display - moved below user info
                                  SizedBox(height: 12),
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.badge, color: Colors.white, size: 20),
                                    SizedBox(width: 8),
                                    Text(
                                      'DS ID: ${_dsID ?? 'Loading...'}',
                                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: Colors.white),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 16),

                      // Quick Actions Card
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: AppTheme.primaryColor.withOpacity(0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.dashboard_customize,
                                      color: AppTheme.primaryColor,
                                      size: 20,
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    context.translate('quick_actions'),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.textPrimaryColor,
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildQuickActionButton(
                                      context.translate('beat_schedule'),
                                      Icons.calendar_today,
                                      () => Navigator.pushNamed(context, '/beat_schedule'),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: _buildQuickActionButton(
                                      context.translate('my_network'),
                                      Icons.people,
                                      () => Navigator.pushNamed(context, '/my_network'),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: _buildQuickActionButton(
                                      context.translate('add_outlet'),
                                      Icons.store_mall_directory,
                                      () => Navigator.pushNamed(context, '/add_outlet'),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: _buildQuickActionButton(
                                      context.translate('create_order'),
                                      Icons.shopping_cart,
                                      () => Navigator.pushNamed(context, '/create_order'),
                                    ),
                                  ),
                                ],
                              ),

                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 16),

                      // Enhanced Check-in/Check-out Card
                      Card(
                        elevation: 3,
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(color: isCheckedIn ? Colors.green.withOpacity(0.3) : Colors.transparent, width: 2),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Responsive layout for check-in status
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Status row with icon and status text
                                    Row(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: isCheckedIn ? Colors.green.withOpacity(0.1) : AppTheme.primaryColor.withOpacity(0.1),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            isCheckedIn ? Icons.access_time_filled : Icons.access_time,
                                            color: isCheckedIn ? Colors.green : AppTheme.primaryColor,
                                            size: 24,
                                          ),
                                        ),
                                        SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            isCheckedIn ? context.translate('working_day_in_progress') : context.translate('start_your_day'),
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: isCheckedIn ? Colors.green : AppTheme.textPrimaryColor
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),

                                    // Active status indicator
                                    if (isCheckedIn)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 8.0, left: 44.0),
                                        child: Container(
                                          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                          decoration: BoxDecoration(
                                            color: Colors.green.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: Text(
                                            context.translate('active'),
                                            style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold, fontSize: 12),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                SizedBox(height: 20),
                                // Timer display with improved styling
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                                  decoration: BoxDecoration(
                                    color: isCheckedIn ? Colors.green.withOpacity(0.05) : AppTheme.backgroundColor,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(color: isCheckedIn ? Colors.green.withOpacity(0.2) : Colors.transparent),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.timer,
                                        size: 28,
                                        color: isCheckedIn ? Colors.green : AppTheme.primaryColor,
                                      ),
                                      SizedBox(width: 12),
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            context.translate('time_elapsed'),
                                            style: TextStyle(fontSize: 14, color: AppTheme.textSecondaryColor),
                                          ),
                                          Text(
                                            _formatTime(seconds),
                                            style: TextStyle(
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                              color: isCheckedIn ? Colors.green : AppTheme.primaryColor,
                                              letterSpacing: 1.2,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 20),
                                // Check-in/Check-out button with improved styling
                                SizedBox(
                                  width: double.infinity,
                                  height: 50,
                                  child: ElevatedButton.icon(
                                    onPressed: _toggleCheckIn,
                                    icon: Icon(isCheckedIn ? Icons.logout : Icons.login, size: 20),
                                    label: Text(
                                      isCheckedIn ? context.translate('check_out').toUpperCase() : context.translate('check_in').toUpperCase(),
                                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, letterSpacing: 1),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: isCheckedIn ? Colors.red.shade700 : Colors.green.shade700,
                                      foregroundColor: Colors.white,
                                      elevation: 2,
                                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // Business Dashboard Card removed

                      // Add extra space at the bottom to ensure content isn't hidden behind the navigation panel
                      SizedBox(height: 20),
                    ],
                  ),
                ),
              ),

              // Simplified bottom panel with just the sign out button
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  height: 80, // Much smaller height
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        blurRadius: 15,
                        offset: Offset(0, -5),
                        spreadRadius: 2,
                      ),
                    ],
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 16.0),
                    child: SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton.icon(
                        onPressed: _signOut,
                        icon: Icon(Icons.logout, size: 20),
                        label: Text(context.translate('logout').toUpperCase(), style: TextStyle(fontWeight: FontWeight.bold, letterSpacing: 1)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.errorColor,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          textStyle: TextStyle(fontSize: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// NavigationButton class removed as it's no longer needed
