import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../theme/app_theme.dart';
import '../utils/localization.dart';

class EditOutletScreen extends StatefulWidget {
  final String outletId;
  final String outletName;
  final String? outletAddress;

  const EditOutletScreen({
    Key? key,
    required this.outletId,
    required this.outletName,
    this.outletAddress,
  }) : super(key: key);

  @override
  _EditOutletScreenState createState() => _EditOutletScreenState();
}

class _EditOutletScreenState extends State<EditOutletScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mobileNumberController = TextEditingController();
  final _kycController = TextEditingController();

  bool _isLoading = true;
  bool _isSubmitting = false;
  Map<String, dynamic>? _outletData;

  @override
  void initState() {
    super.initState();
    _fetchOutletData();
  }

  @override
  void dispose() {
    _mobileNumberController.dispose();
    _kycController.dispose();
    super.dispose();
  }

  Future<void> _fetchOutletData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      DocumentSnapshot doc = await FirebaseFirestore.instance
          .collection('outlets')
          .doc(widget.outletId)
          .get();

      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        setState(() {
          _outletData = data;
          _mobileNumberController.text = data['mobileNumber'] ?? '';
          _kycController.text = data['kyc'] ?? '';
          _isLoading = false;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.translate('outlet_not_found'))),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      print('Error fetching outlet data: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${context.translate('error')}: $e')),
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateOutlet() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Create update map with required fields
      Map<String, dynamic> updateData = {
        'mobileNumber': _mobileNumberController.text,
        'kyc': _kycController.text, // Always include KYC field, even if empty
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Print debug information
      print('Updating outlet with ID: ${widget.outletId}');
      print('KYC value before update: ${_outletData?['kyc'] ?? 'null'}');
      print('KYC value being set: ${_kycController.text}');

      await FirebaseFirestore.instance
          .collection('outlets')
          .doc(widget.outletId)
          .update(updateData);

      // Verify the update was successful by fetching the document again
      DocumentSnapshot updatedDoc = await FirebaseFirestore.instance
          .collection('outlets')
          .doc(widget.outletId)
          .get();

      Map<String, dynamic> updatedData = updatedDoc.data() as Map<String, dynamic>;
      print('KYC value after update: ${updatedData['kyc'] ?? 'null'}');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(context.translate('outlet_updated_successfully')),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.pop(context, true); // Return true to indicate successful update
    } catch (e) {
      print('Error updating outlet: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${context.translate('error')}: $e'),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('edit_outlet')),
        elevation: 0,
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Container(
              color: Colors.grey.shade50,
              child: Column(
                children: [
                  // Header with outlet details
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Outlet name
                        Text(
                          widget.outletName,
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 8),
                        // Outlet address
                        if (widget.outletAddress != null && widget.outletAddress!.isNotEmpty)
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(Icons.location_on, color: Colors.white70, size: 16),
                              SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  widget.outletAddress!,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),

                  // Form
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(20),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section title
                            Text(
                              context.translate('edit_outlet_details'),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            SizedBox(height: 6),
                            Text(
                              context.translate('edit_outlet_description'),
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                            SizedBox(height: 24),

                            // Mobile Number
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    context.translate('phone_number'),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.textPrimaryColor,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  TextFormField(
                                    controller: _mobileNumberController,
                                    decoration: InputDecoration(
                                      hintText: context.translate('enter_mobile_number'),
                                      prefixIcon: Icon(Icons.phone, color: AppTheme.primaryColor),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return context.translate('please_enter_mobile_number');
                                      }
                                      if (value.length != 10) {
                                        return context.translate('mobile_number_should_be_10_digits');
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    context.translate('mobile_number_required'),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.textSecondaryColor,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 20),

                            // KYC
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 4,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    context.translate('kyc'),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.textPrimaryColor,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  TextFormField(
                                    controller: _kycController,
                                    decoration: InputDecoration(
                                      hintText: context.translate('enter_kyc_optional'),
                                      prefixIcon: Icon(Icons.verified_user, color: AppTheme.primaryColor),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                                    ),
                                    // No validator since KYC is optional
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    context.translate('kyc_optional'),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.textSecondaryColor,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Bottom action bar with update button
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton.icon(
                        onPressed: _isSubmitting ? null : _updateOutlet,
                        icon: _isSubmitting
                            ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                            : Icon(Icons.save),
                        label: Text(
                          _isSubmitting ? context.translate('updating') : context.translate('update'),
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, letterSpacing: 1),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: Colors.grey.shade400,
                          elevation: 2,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
