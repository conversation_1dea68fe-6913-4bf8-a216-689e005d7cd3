package com.example.ds_panel

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.Tasks
import com.google.firebase.appcheck.AppCheckProvider
import com.google.firebase.appcheck.AppCheckToken
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import java.security.MessageDigest
import java.util.*
import java.util.concurrent.TimeUnit

class CustomAppCheckProvider(private val context: Context) : AppCheckProvider {
    companion object {
        private const val TAG = "CustomAppCheckProvider"
        private const val TOKEN_TTL_MILLIS = 3600000L // 1 hour
        private const val BACKEND_URL = "https://generateappchecktoken-5yw2qsexaa-uc.a.run.app"
    }

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    private val coroutineScope = CoroutineScope(Dispatchers.IO + Job())

    override fun getToken(): Task<AppCheckToken> {
        return Tasks.call {
            try {
                val token = runBlocking {
                    withContext(Dispatchers.IO) {
                        requestTokenFromBackend()
                    }
                }
                Log.d(TAG, "✅ Token obtained successfully")
                CustomToken(token)
            } catch (e: Exception) {
                Log.e(TAG, "❌ Failed to get token: ${e.message}")
                throw e
            }
        }
    }

    private suspend fun requestTokenFromBackend(): String = withContext(Dispatchers.IO) {
        val requestBody = JSONObject().apply {
            put("packageName", context.packageName)
            put("timestamp", System.currentTimeMillis())
            put("deviceId", getDeviceId())
            put("signature", getAppSignature())
            put("buildType", if (BuildConfig.DEBUG) "debug" else "release")
        }.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(BACKEND_URL)
            .post(requestBody)
            .build()

        client.newCall(request).execute().use { response ->
            if (!response.isSuccessful) {
                throw IOException("Unexpected response ${response.code}")
            }

            val jsonResponse = JSONObject(response.body?.string() ?: throw IOException("Empty response"))
            if (!jsonResponse.has("token")) {
                throw IOException("Invalid response: missing token")
            }

            jsonResponse.getString("token")
        }
    }

    @Suppress("DEPRECATION")
    private fun getAppVersion(): String {
        return try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.packageManager.getPackageInfo(context.packageName, PackageManager.PackageInfoFlags.of(0))
            } else {
                context.packageManager.getPackageInfo(context.packageName, 0)
            }
            "${packageInfo.versionName}:${packageInfo.versionCode}"
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get app version", e)
            "unknown"
        }
    }

    @Suppress("DEPRECATION")
    private fun getAppSignature(): String {
        return try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                context.packageManager.getPackageInfo(context.packageName, PackageManager.GET_SIGNING_CERTIFICATES)
            } else {
                context.packageManager.getPackageInfo(context.packageName, PackageManager.GET_SIGNATURES)
            }

            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo.apkContentsSigners
            } else {
                packageInfo.signatures
            }

            if (signatures.isNotEmpty()) {
                val md = MessageDigest.getInstance("SHA-256")
                val signature = signatures[0]
                md.update(signature.toByteArray())
                Base64.getEncoder().encodeToString(md.digest())
            } else {
                throw Exception("No signatures found")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get app signature", e)
            "unknown"
        }
    }

    private fun getDeviceId(): String {
        val deviceInfo = "${Build.BOARD}:${Build.BRAND}:${Build.DEVICE}:${Build.HARDWARE}:${Build.ID}"
        return UUID.nameUUIDFromBytes(deviceInfo.toByteArray()).toString()
    }

    private class CustomToken(private val token: String) : AppCheckToken() {
        private val expirationTimestamp = System.currentTimeMillis() + TOKEN_TTL_MILLIS

        override fun getToken(): String = token
        override fun getExpireTimeMillis(): Long = expirationTimestamp
    }
}
