import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'services/token_refresh_service.dart';
import 'services/app_check_service.dart';
import 'config/app_check_config.dart';
import 'screens/welcome_screen.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'screens/order_creation_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/check_in_screen.dart';
import 'screens/my_network_screen.dart';
import 'screens/add_outlet_screen.dart';
import 'screens/notifications_screen.dart';

import 'screens/beat_schedule_screen.dart';
import 'theme/app_theme.dart';

import 'services/notification_service.dart';
import 'services/fcm_service.dart';
import 'services/language_service.dart';
import 'services/custom_app_check_provider.dart';
import 'utils/localization.dart';
import 'utils/asset_debug.dart';
import 'utils/file_utils.dart';

// Import the background handler
import 'services/fcm_service.dart' show firebaseMessagingBackgroundHandler;

// Global navigator key for accessing navigator from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();


Future<void> initializeAppCheck() async {
  print('📱 Initializing Firebase App Check...');
  bool appCheckInitialized = false;
  
  // First try Play Integrity in release mode or custom token in sideloaded APK
  if (kReleaseMode) {
    try {
      // Step 1: Try Play Integrity first
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.deviceCheck,
      );
      print('✅ Play Integrity activated successfully');
      
      // Verify token generation succeeds
      final token = await FirebaseAppCheck.instance.getToken(true);
      if (token != null) {
        print('✅ App Check token generated via Play Integrity');
        appCheckInitialized = true;
      }
    } catch (e) {
      print('⚠️ Play Integrity activation failed, falling back to custom provider: $e');
      
      // Step 2: Fall back to custom token provider
      try {
        // Initialize AppCheckService first
        final appCheckService = await AppCheckService.initialize();
        await appCheckService.validateAndRefreshToken(force: true);
        
        print('✅ Custom App Check provider initialized successfully');
        appCheckInitialized = true;
      } catch (e) {
        print('❌ Custom token provider failed: $e');
      }
    }
  }
  
  // For debug builds, use debug provider
  if (!appCheckInitialized && !kReleaseMode) {
    try {
      print('🔧 Activating debug provider for development...');
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.debug,
        appleProvider: AppleProvider.debug,
      );
      appCheckInitialized = true;
    } catch (e) {
      print('❌ Debug provider activation failed: $e');
    }
  }
  
  if (!appCheckInitialized) {
    print('❌ WARNING: App Check initialization failed. The app may not work correctly.');
  }

  // Set up token change listener
  FirebaseAppCheck.instance.onTokenChange.listen(
    (token) {
      print('🔄 App Check token refreshed');
    },
    onError: (error) {
      print('❌ Error refreshing App Check token: $error');
    }
  );
}

Future<void> main() async {
  try {
    // Ensure Flutter is initialized
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize Firebase first
    await Firebase.initializeApp();
    print('✅ Firebase core initialized successfully');

    // Initialize App Check with proper fallback chain - temporarily disabled for debugging
    // await initializeAppCheck();
    // print('✅ App Check initialization completed');    // Enable auto token refresh
    // await FirebaseAppCheck.instance.setTokenAutoRefreshEnabled(true);
    // print('✅ Token auto-refresh enabled');
    print('⚠️ App Check temporarily disabled for debugging');

    // Initialize token refresh service
    await TokenRefreshService().initialize();
    print('✅ Token refresh service initialized');

    // Initialize other services in parallel
    await Future.wait([
      NotificationService().init(navigatorKey),
      FCMService().init(navigatorKey),
      LanguageService().init(),
    ]);

    // Set up FCM background handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Debug assets in development
    if (!kReleaseMode) {
      await AssetDebug.listAssets();
    }

    // Copy language files to local storage for fallback
    await FileUtils.copyLanguageFilesToLocal();

    // Run the app with Phoenix for hot restart support
    runApp(Phoenix(child: MyApp()));
  } catch (e, stack) {
    print('❌ Error during app initialization: $e');
    print('Stack trace: $stack');
    // Show a user-friendly error screen in production
    if (kReleaseMode) {
      runApp(MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('Something went wrong. Please restart the app.'),
          ),
        ),
      ));
    } else {
      rethrow; // Re-throw in debug mode for better debugging
    }
  }
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();

  // This allows the app to be rebuilt when the language changes
  static void restartApp(BuildContext context) {
    final _MyAppState? state = context.findAncestorStateOfType<_MyAppState>();
    state?.restartApp();
  }
}

class _MyAppState extends State<MyApp> {
  DateTime? _lastPressed;
  bool _isUserLoggedIn = false;
  final LanguageService _languageService = LanguageService();

  // Key that changes when the language changes
  Key _appKey = UniqueKey();

  void restartApp() {
    setState(() {
      _appKey = UniqueKey();
    });
  }

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    // Check if the user is already signed in
    User? user = FirebaseAuth.instance.currentUser;
    setState(() {
      _isUserLoggedIn = user != null;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use a simple approach without ValueListenableBuilder
    Locale locale = _languageService.currentLocale;
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) return;

        final now = DateTime.now();
        if (_lastPressed == null || now.difference(_lastPressed!) > Duration(seconds: 2)) {
          _lastPressed = now;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Press back again to exit"),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          // Exit app on double press within 2 seconds
          exit(0);
        }
      },
      child: MaterialApp(
        key: _appKey,
        title: 'Quickk DS',
        theme: AppTheme.lightTheme,
        navigatorKey: navigatorKey, // Add navigator key for notification navigation
        initialRoute: _isUserLoggedIn ? '/dashboard' : '/',

        // Localization settings
        locale: locale,
        supportedLocales: LanguageService.SUPPORTED_LOCALES,
        localizationsDelegates: [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],  localeResolutionCallback: (locale, supportedLocales) {
          print('Locale resolution callback called with: ${locale?.languageCode}');
          // Check if the current device locale is supported
          for (var supportedLocale in supportedLocales) {
            if (supportedLocale.languageCode == locale?.languageCode) {
              print('Found matching locale: ${supportedLocale.languageCode}');
              return supportedLocale;
            }
          }
          // If the locale is not supported, use the first one (English)
          print('No matching locale found, using default: ${supportedLocales.first.languageCode}');
          return supportedLocales.first;
        },

        routes: {
          '/': (context) => WelcomeScreen(),
          '/login': (context) => LoginScreen(),
          '/dashboard': (context) => DashboardScreen(),
          '/create_order': (context) => CreateOrderScreen(),
          '/profile': (context) => ProfileScreen(),
          '/check_in': (context) => CheckInScreen(),
          '/my_network': (context) => MyNetworkScreen(),
          '/add_outlet': (context) => AddOutletScreen(),
          '/notifications': (context) => NotificationsScreen(),
          '/beat_schedule': (context) => BeatScheduleScreen(),
        },
      ),
    );
  }
}