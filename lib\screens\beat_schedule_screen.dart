import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../utils/localization.dart';
import '../theme/app_theme.dart';
import '../services/user_data_service.dart';

class BeatScheduleScreen extends StatefulWidget {
  @override
  _BeatScheduleScreenState createState() => _BeatScheduleScreenState();
}

class _BeatScheduleScreenState extends State<BeatScheduleScreen> {
  bool isLoading = true;
  String? _dsId;
  String? _userName;
  Map<String, List<String>> weekdayBeats = {
    'Monday': [],
    'Tuesday': [],
    'Wednesday': [],
    'Thursday': [],
    'Friday': [],
    'Saturday': [],
    'Sunday': [],
  };

  @override
  void initState() {
    super.initState();
    _fetchUserInfo().then((_) {
      _fetchBeatSchedule();
    });
  }

  Future<void> _fetchUserInfo() async {
    try {
      // Get the user document ID from our service
      String? userDocId = await UserDataService().getUserDocumentId();

      if (userDocId != null) {
        // Fetch the user document using the correct document ID
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(userDocId).get();

        if (mounted) {
          setState(() {
            _dsId = userDoc['dsId'];
            _userName = userDoc['name'];
          });
        }

        print('Fetched user info - DS ID: $_dsId, Name: $_userName');
      } else {
        print('User document ID not found');
      }
    } catch (e) {
      print('Error fetching user info: $e');
    }
  }

  Future<void> _fetchBeatSchedule() async {
    if (_dsId == null || _userName == null) {
      print('DS ID or User Name is null, cannot fetch beat schedule');
      setState(() {
        isLoading = false;
      });
      return;
    }

    try {
      // Construct the dsName (format: userName_dsId)
      String dsName = '${_userName}_${_dsId}';
      print('Constructed dsName: $dsName');

      // Initialize empty weekday beats map
      Map<String, List<String>> tempWeekdayBeats = {
        'Monday': [],
        'Tuesday': [],
        'Wednesday': [],
        'Thursday': [],
        'Friday': [],
        'Saturday': [],
        'Sunday': [],
      };

      // First try to fetch beats by dsName
      QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('beats')
          .where('dsName', isEqualTo: dsName)
          .get();

      print('Found ${snapshot.docs.length} beats with dsName: $dsName');

      // If no beats found with dsName, try fetching by dsId as fallback
      if (snapshot.docs.isEmpty) {
        print('No beats found with dsName, trying dsId');
        snapshot = await FirebaseFirestore.instance
            .collection('beats')
            .where('dsId', isEqualTo: _dsId)
            .get();

        print('Found ${snapshot.docs.length} beats with dsId: $_dsId');
      }

      // Process the beats and organize them by weekday
      for (var doc in snapshot.docs) {
        String beatName = doc['beatName'] as String;
        List<dynamic> weekdays = doc['daysOfVisit'] ?? doc['weekdays'] ?? [];

        print('Beat: $beatName, Days of visit: $weekdays');

        // Add the beat to each weekday it's assigned to
        for (var weekday in weekdays) {
          if (tempWeekdayBeats.containsKey(weekday)) {
            tempWeekdayBeats[weekday]!.add(beatName);
          }
        }
      }

      if (mounted) {
        setState(() {
          weekdayBeats = tempWeekdayBeats;
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching beat schedule: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the current day of the week
    String today = _getCurrentDayOfWeek();

    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('beat_schedule')),
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with title
                  Text(
                    context.translate('weekly_beat_schedule'),
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  SizedBox(height: 16),

                  // Schedule table
                  Expanded(
                    child: ListView.builder(
                      itemCount: weekdayBeats.length,
                      itemBuilder: (context, index) {
                        String weekday = weekdayBeats.keys.elementAt(index);
                        List<String> beats = weekdayBeats[weekday]!;
                        bool isToday = weekday == today;

                        return Card(
                          elevation: isToday ? 3 : 1,
                          margin: EdgeInsets.only(bottom: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: isToday ? AppTheme.primaryColor : Colors.transparent,
                              width: isToday ? 2 : 0,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Weekday header
                                Row(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: isToday
                                            ? AppTheme.primaryColor.withOpacity(0.2)
                                            : AppTheme.primaryColor.withOpacity(0.1),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.calendar_today,
                                        color: isToday ? AppTheme.primaryColor : Colors.grey,
                                        size: 20,
                                      ),
                                    ),
                                    SizedBox(width: 12),
                                    Text(
                                      weekday,
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: isToday ? AppTheme.primaryColor : AppTheme.textPrimaryColor,
                                      ),
                                    ),
                                    if (isToday)
                                      Container(
                                        margin: EdgeInsets.only(left: 8),
                                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: AppTheme.primaryColor.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          context.translate('today'),
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: AppTheme.primaryColor,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                SizedBox(height: 12),

                                // Beats list
                                beats.isEmpty
                                    ? Padding(
                                        padding: const EdgeInsets.only(left: 40.0),
                                        child: Text(
                                          context.translate('no_beats_assigned'),
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      )
                                    : ListView.builder(
                                        shrinkWrap: true,
                                        physics: NeverScrollableScrollPhysics(),
                                        itemCount: beats.length,
                                        itemBuilder: (context, beatIndex) {
                                          return Padding(
                                            padding: const EdgeInsets.only(left: 40.0, bottom: 8.0),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.location_on,
                                                  size: 16,
                                                  color: isToday ? AppTheme.primaryColor : Colors.grey,
                                                ),
                                                SizedBox(width: 8),
                                                Text(
                                                  beats[beatIndex],
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    color: AppTheme.textPrimaryColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  String _getCurrentDayOfWeek() {
    final now = DateTime.now();
    final days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    // DateTime's weekday is 1-based (1 = Monday, 7 = Sunday)
    return days[now.weekday - 1];
  }
}
