/**
 * Server-side Custom App Check Token Validation Example
 * This is a Node.js example for validating custom App Check tokens
 * Deploy this to your server (Firebase Functions, Express.js, etc.)
 */

const crypto = require('crypto');

// Configuration - match these with your app
const APP_SECRET = 'quickk-ds-panel-custom-secret-2024';
const TOKEN_TTL_MILLIS = 3600000; // 1 hour

/**
 * Validate custom App Check token
 * @param {string} token - The custom token from the app
 * @param {string} expectedSignature - Expected app signature (optional)
 * @returns {object} Validation result
 */
function validateCustomAppCheckToken(token, expectedSignature = null) {
    try {
        // Decode the token
        const decodedToken = Buffer.from(token, 'base64').toString('utf-8');
        const [hash, timestamp] = decodedToken.split(':');
        
        if (!hash || !timestamp) {
            return { valid: false, error: 'Invalid token format' };
        }
        
        // Check token expiry
        const tokenTimestamp = parseInt(timestamp);
        const currentTime = Date.now();
        const tokenAge = currentTime - tokenTimestamp;
        
        if (tokenAge > TOKEN_TTL_MILLIS) {
            return { valid: false, error: 'Token expired' };
        }
        
        // For production, you would validate the hash against known app signatures
        // This is a simplified example
        console.log('Token validation:', {
            hash: hash.substring(0, 16) + '...',
            timestamp: new Date(tokenTimestamp).toISOString(),
            age: `${Math.round(tokenAge / 1000)}s`,
            valid: true
        });
        
        return { 
            valid: true, 
            timestamp: tokenTimestamp,
            age: tokenAge,
            hash: hash
        };
        
    } catch (error) {
        return { valid: false, error: error.message };
    }
}

/**
 * Express.js middleware for App Check validation
 */
function validateAppCheckMiddleware(req, res, next) {
    const appCheckToken = req.headers['x-firebase-appcheck'];
    
    if (!appCheckToken) {
        return res.status(401).json({ error: 'Missing App Check token' });
    }
    
    const validation = validateCustomAppCheckToken(appCheckToken);
    
    if (!validation.valid) {
        return res.status(401).json({ error: `Invalid App Check token: ${validation.error}` });
    }
    
    // Token is valid, continue
    req.appCheckValidation = validation;
    next();
}

/**
 * Firebase Functions example
 */
const functions = require('firebase-functions');

exports.validateAppCheck = functions.https.onRequest((req, res) => {
    const token = req.body.token || req.headers['x-firebase-appcheck'];
    
    if (!token) {
        return res.status(400).json({ error: 'No token provided' });
    }
    
    const validation = validateCustomAppCheckToken(token);
    
    res.json({
        valid: validation.valid,
        error: validation.error || null,
        timestamp: validation.timestamp || null,
        age: validation.age || null
    });
});

/**
 * Example usage in your API endpoints
 */
function exampleProtectedEndpoint(req, res) {
    // This endpoint is protected by App Check
    validateAppCheckMiddleware(req, res, () => {
        // Your protected logic here
        res.json({ 
            message: 'Access granted!',
            appCheckValidation: req.appCheckValidation
        });
    });
}

module.exports = {
    validateCustomAppCheckToken,
    validateAppCheckMiddleware,
    exampleProtectedEndpoint
};

/**
 * Usage Instructions:
 * 
 * 1. Deploy this to your server (Firebase Functions, Express.js, etc.)
 * 
 * 2. In your app, include the App Check token in requests:
 *    ```dart
 *    final token = await FirebaseAppCheck.instance.getToken();
 *    final response = await http.get(
 *      Uri.parse('your-api-endpoint'),
 *      headers: {'x-firebase-appcheck': token},
 *    );
 *    ```
 * 
 * 3. For Firebase Functions, set up the validation:
 *    ```javascript
 *    exports.myProtectedFunction = functions.https.onRequest((req, res) => {
 *      const validation = validateCustomAppCheckToken(req.headers['x-firebase-appcheck']);
 *      if (!validation.valid) {
 *        return res.status(401).json({ error: validation.error });
 *      }
 *      // Your protected logic here
 *    });
 *    ```
 * 
 * 4. For production, enhance validation by:
 *    - Storing known app signatures in database
 *    - Implementing rate limiting
 *    - Adding device fingerprinting
 *    - Using JWT tokens for additional security
 */
