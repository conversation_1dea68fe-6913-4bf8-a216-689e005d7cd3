/**
 * Firebase Functions for DS Panel Custom App Check
 * Deploy this to Firebase Functions
 */

const functions = require('firebase-functions');
const admin = require('firebase-admin');
const crypto = require('crypto');

// Initialize Firebase Admin SDK
admin.initializeApp();

// Configuration
const APP_ID = 'com.example.ds_panel'; // Your Android app ID
const APP_SECRET = 'quickk-ds-panel-custom-secret-2024'; // Your app secret
const TOKEN_TTL_SECONDS = 3600; // 1 hour

/**
 * Validate custom credentials from your app
 */
function validateCustomCredentials(credentials) {
    try {
        const { deviceSecret, appSignature, deviceInfo, timestamp } = credentials;
        
        // Validate timestamp (not too old)
        const now = Date.now();
        const credentialAge = now - timestamp;
        const maxAge = 5 * 60 * 1000; // 5 minutes
        
        if (credentialAge > maxAge) {
            return { valid: false, error: 'Credentials too old' };
        }
        
        // Validate device secret format
        if (!deviceSecret || deviceSecret.length < 32) {
            return { valid: false, error: 'Invalid device secret' };
        }
        
        // Validate app signature
        if (!appSignature || appSignature.length < 32) {
            return { valid: false, error: 'Invalid app signature' };
        }
        
        // Generate expected hash
        const expectedPayload = `${APP_SECRET}:${appSignature}:${deviceInfo}:${timestamp}`;
        const expectedHash = crypto.createHash('sha256').update(expectedPayload).digest('hex');
        
        // Validate device secret matches expected hash
        if (deviceSecret !== expectedHash) {
            return { valid: false, error: 'Invalid credentials hash' };
        }
        
        console.log('Custom credentials validated successfully:', {
            deviceInfo: deviceInfo.substring(0, 20) + '...',
            timestamp: new Date(timestamp).toISOString(),
            age: `${Math.round(credentialAge / 1000)}s`
        });
        
        return { valid: true };
        
    } catch (error) {
        return { valid: false, error: error.message };
    }
}

/**
 * Generate App Check token using Firebase Admin SDK
 */
async function generateAppCheckToken(appId, customClaims = {}) {
    try {
        // Create App Check token with custom claims
        const appCheckToken = await admin.appCheck().createToken(appId, {
            ttlMillis: TOKEN_TTL_SECONDS * 1000,
            customClaims: customClaims
        });
        
        console.log('App Check token generated successfully:', {
            appId: appId,
            tokenLength: appCheckToken.token.length,
            ttlSeconds: TOKEN_TTL_SECONDS
        });
        
        return {
            success: true,
            token: appCheckToken.token,
            ttlMillis: appCheckToken.ttlMillis,
            customClaims: customClaims
        };
        
    } catch (error) {
        console.error('Error generating App Check token:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Firebase Function: Generate App Check Token
 * URL: https://your-project-id.cloudfunctions.net/generateAppCheckToken
 */
exports.generateAppCheckToken = functions.https.onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    
    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }
    
    try {
        const { credentials } = req.body;
        
        if (!credentials) {
            return res.status(400).json({ error: 'Missing credentials' });
        }
        
        // Step 1: Validate custom credentials
        const validation = validateCustomCredentials(credentials);
        
        if (!validation.valid) {
            return res.status(401).json({ 
                error: 'Invalid credentials',
                details: validation.error
            });
        }
        
        // Step 2: Generate App Check token
        const customClaims = {
            deviceInfo: credentials.deviceInfo,
            timestamp: credentials.timestamp,
            source: 'custom-backend'
        };
        
        const tokenResult = await generateAppCheckToken(APP_ID, customClaims);
        
        if (!tokenResult.success) {
            return res.status(500).json({
                error: 'Failed to generate App Check token',
                details: tokenResult.error
            });
        }
        
        // Step 3: Return App Check token
        res.json({
            success: true,
            appCheckToken: tokenResult.token,
            ttlMillis: tokenResult.ttlMillis,
            expiresAt: new Date(Date.now() + tokenResult.ttlMillis).toISOString(),
            message: 'App Check token generated successfully'
        });
        
    } catch (error) {
        console.error('Error in generateAppCheckToken function:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Firebase Function: Validate App Check Token
 * URL: https://your-project-id.cloudfunctions.net/validateAppCheckToken
 */
exports.validateAppCheckToken = functions.https.onRequest(async (req, res) => {
    // Enable CORS
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type');
    
    if (req.method === 'OPTIONS') {
        res.status(204).send('');
        return;
    }
    
    try {
        const { token } = req.body;
        
        if (!token) {
            return res.status(400).json({ error: 'Missing token' });
        }
        
        // Verify App Check token
        const decodedToken = await admin.appCheck().verifyToken(token);
        
        res.json({
            valid: true,
            appId: decodedToken.app_id,
            customClaims: decodedToken.custom_claims || {},
            message: 'App Check token is valid'
        });
        
    } catch (error) {
        console.error('Error validating App Check token:', error);
        res.status(401).json({
            valid: false,
            error: 'Invalid App Check token',
            details: error.message
        });
    }
});

/**
 * Health check function
 */
exports.healthCheck = functions.https.onRequest((req, res) => {
    res.json({
        status: 'healthy',
        service: 'DS Panel Custom App Check Service',
        timestamp: new Date().toISOString(),
        functions: [
            'generateAppCheckToken',
            'validateAppCheckToken',
            'healthCheck'
        ]
    });
});

/**
 * Deployment Instructions:
 * 
 * 1. Install Firebase CLI: npm install -g firebase-tools
 * 2. Login: firebase login
 * 3. Go to your project directory
 * 4. Initialize functions: firebase init functions
 * 5. Replace functions/index.js with this code
 * 6. Deploy: firebase deploy --only functions
 * 
 * Your functions will be available at:
 * https://YOUR-PROJECT-ID.cloudfunctions.net/generateAppCheckToken
 * https://YOUR-PROJECT-ID.cloudfunctions.net/validateAppCheckToken
 * https://YOUR-PROJECT-ID.cloudfunctions.net/healthCheck
 */
