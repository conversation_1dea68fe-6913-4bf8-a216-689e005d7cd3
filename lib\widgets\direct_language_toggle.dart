import 'package:flutter/material.dart';
import '../services/language_service.dart';

class DirectLanguageToggle extends StatefulWidget {
  @override
  _DirectLanguageToggleState createState() => _DirectLanguageToggleState();
}

class _DirectLanguageToggleState extends State<DirectLanguageToggle> {
  final LanguageService _languageService = LanguageService();

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () {
        // Toggle language
        _languageService.toggleLanguage();

        // Update the UI
        setState(() {});

        // Show a message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Language changed to ${_languageService.currentLanguageName}'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          _languageService.currentLocale.languageCode.toUpperCase(),
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
