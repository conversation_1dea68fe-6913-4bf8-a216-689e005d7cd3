package com.example.ds_panel

import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.AppCheckProvider
import com.google.firebase.appcheck.AppCheckProviderFactory

/**
 * Custom App Check Provider Factory for direct APK distribution
 * This factory creates instances of our custom App Check provider
 */
class CustomAppCheckProviderFactory : AppCheckProviderFactory {
    companion object {
        private const val TAG = "CustomAppCheckFactory"
        private var instance: CustomAppCheckProviderFactory? = null

        /**
         * Get singleton instance of the factory
         */
        @JvmStatic
        @Synchronized
        fun getInstance(): CustomAppCheckProviderFactory {
            return instance ?: CustomAppCheckProviderFactory().also {
                instance = it
                Log.d(TAG, "✅ Created new CustomAppCheckProviderFactory instance")
            }
        }
    }

    override fun create(firebaseApp: FirebaseApp): AppCheckProvider {
        Log.d(TAG, "Creating CustomAppCheckProvider for ${firebaseApp.name}")
        return CustomAppCheckProvider(firebaseApp.applicationContext)
    }
}
