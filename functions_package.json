{"name": "ds-panel-appcheck-functions", "version": "1.0.0", "description": "Firebase Functions for DS Panel Custom App Check", "main": "index.js", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}