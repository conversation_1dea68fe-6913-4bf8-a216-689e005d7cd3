import 'package:flutter/material.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import '../services/language_service.dart';
import '../utils/localization.dart';
import '../main.dart';

class LanguageSwitcher extends StatefulWidget {
  @override
  _LanguageSwitcherState createState() => _LanguageSwitcherState();
}

class _LanguageSwitcherState extends State<LanguageSwitcher> {
  final LanguageService _languageService = LanguageService();

  @override
  void initState() {
    super.initState();
    // Listen for language changes
    _languageService.localeStream.addListener(_onLanguageChanged);
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    _languageService.localeStream.removeListener(_onLanguageChanged);
    super.dispose();
  }

  void _onLanguageChanged() {
    // Trigger a rebuild when language changes
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.language),
          SizedBox(width: 4),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.white.withOpacity(0.5), width: 1),
            ),
            child: Text(
              _languageService.currentLocale.languageCode.toUpperCase(),
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      tooltip: 'Change Language',
      onSelected: (String languageCode) async {
        try {
          // Change language
          if (languageCode == 'en') {
            await _languageService.changeLanguage(LanguageService.ENGLISH);
          } else if (languageCode == 'hi') {
            await _languageService.changeLanguage(LanguageService.HINDI);
          }

          // Show message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Language changed to ${_languageService.getLanguageName(
                _languageService.currentLocale
              )}'),
              duration: Duration(seconds: 2),
            ),
          );

          // Restart the app using Phoenix
          Phoenix.rebirth(context);
        } catch (e) {
          print('Error changing language: $e');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error changing language: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'en',
          child: Row(
            children: [
              Text('EN', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(width: 8),
              Text('English'),
              if (_languageService.currentLocale.languageCode == 'en')
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Icon(Icons.check, size: 16, color: Colors.green),
                ),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'hi',
          child: Row(
            children: [
              Text('HI', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(width: 8),
              Text('Hindi'),
              if (_languageService.currentLocale.languageCode == 'hi')
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Icon(Icons.check, size: 16, color: Colors.green),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
