import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'notification_service.dart';
import 'user_data_service.dart';

class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final NotificationService _notificationService = NotificationService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  bool _isInitialized = false;
  String? _fcmToken;

  Future<void> init(GlobalKey<NavigatorState> navigatorKey) async {
    if (_isInitialized) return;

    try {
      // Check if the plugin is available
      bool isAvailable = await _checkFCMAvailability();
      if (!isAvailable) {
        print('FCM: Firebase Messaging plugin is not available');
        return;
      }

      // Request permission for iOS and Android 13+
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        announcement: true,
        carPlay: false,
        criticalAlert: false,
      );

      print('FCM: User granted permission: ${settings.authorizationStatus}');

      // Get FCM token
      _fcmToken = await _firebaseMessaging.getToken();
      print('FCM: Token: $_fcmToken');

      // Save the token to Firestore
      await _saveFCMTokenToFirestore();

      // Handle token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        print('FCM: Token refreshed: $newToken');
        _fcmToken = newToken;
        _saveFCMTokenToFirestore();
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('FCM: Got a message whilst in the foreground!');
        print('FCM: Message data: ${message.data}');

        if (message.notification != null) {
          print('FCM: Message also contained a notification: ${message.notification}');

          // Extract notification data
          String title = message.notification?.title ?? 'New Notification';
          String body = message.notification?.body ?? '';
          Map<String, dynamic> data = message.data;
          String payload = data.toString();
          String? messageId = message.messageId;

          // Add a unique identifier to the payload to prevent duplicates
          String enhancedPayload = messageId != null ?
              '{"fcmMessageId":"$messageId", "data":$payload}' : payload;

          // Show a local notification - this will also save to Firestore
          _notificationService.showNotification(
            title: title,
            body: body,
            payload: enhancedPayload,
            fromFCM: true, // Flag to indicate this is from FCM
            messageId: messageId, // Pass the message ID to prevent duplicates
          );
        }
      });

      // Handle notification click when app is in background
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('FCM: A notification was clicked on: ${message.data}');
        navigatorKey.currentState?.pushNamed('/notifications');
      });

      // Check if app was opened from a notification
      RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        print('FCM: App opened from notification: ${initialMessage.data}');
        // Wait for the app to fully initialize before navigating
        Future.delayed(Duration(seconds: 1), () {
          navigatorKey.currentState?.pushNamed('/notifications');
        });
      }

      // Subscribe to topics
      await subscribeToTopic('ds_all');

      // Subscribe to user-specific topic if logged in
      User? currentUser = _auth.currentUser;
      if (currentUser != null) {
        await subscribeToTopic('user_${currentUser.uid}');
      }

      _isInitialized = true;
      print('FCM service initialized successfully');
    } catch (e) {
      print('FCM: Error initializing FCM service: $e');
    }
  }

  // Check if FCM is available
  Future<bool> _checkFCMAvailability() async {
    try {
      // Try to get the token as a simple test
      await _firebaseMessaging.getToken();
      return true;
    } catch (e) {
      print('FCM not available: $e');
      return false;
    }
  }

  // Save FCM token to Firestore
  Future<void> _saveFCMTokenToFirestore() async {
    try {
      if (_fcmToken == null) return;

      User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        print('FCM: Cannot save token - no user logged in');
        return;
      }

      // Get the correct user document ID from UserDataService
      String? userDocId = await UserDataService().getUserDocumentId();
      if (userDocId == null) {
        print('FCM: Cannot save token - user document ID not found');
        return;
      }

      print('FCM: Saving token for user document: $userDocId');

      // Save to user document using set with merge option to avoid errors if document doesn't exist
      await _firestore.collection('users').doc(userDocId).set({
        'fcmToken': _fcmToken,
        'tokenUpdatedAt': FieldValue.serverTimestamp(),
        'platform': kIsWeb ? 'web' : 'mobile',
      }, SetOptions(merge: true));

      // Also save to a separate tokens collection for easier querying
      await _firestore.collection('fcm_tokens').doc(userDocId).set({
        'userId': userDocId,
        'token': _fcmToken,
        'updatedAt': FieldValue.serverTimestamp(),
        'platform': kIsWeb ? 'web' : 'mobile',
        'appType': 'ds_panel',
      }, SetOptions(merge: true));

      print('FCM: Token saved to Firestore');
    } catch (e) {
      print('FCM: Error saving token to Firestore: $e');
    }
  }

  // Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      // Check if FCM is available first
      bool isAvailable = await _checkFCMAvailability();
      if (!isAvailable) {
        print('FCM: Cannot subscribe to topic - Firebase Messaging plugin is not available');
        return;
      }

      await _firebaseMessaging.subscribeToTopic(topic);
      print('FCM: Subscribed to topic: $topic');

      // Record subscription in Firestore
      await _recordTopicSubscription(topic, true);
    } catch (e) {
      print('FCM: Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // Check if FCM is available first
      bool isAvailable = await _checkFCMAvailability();
      if (!isAvailable) {
        print('FCM: Cannot unsubscribe from topic - Firebase Messaging plugin is not available');
        return;
      }

      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('FCM: Unsubscribed from topic: $topic');

      // Record unsubscription in Firestore
      await _recordTopicSubscription(topic, false);
    } catch (e) {
      print('FCM: Error unsubscribing from topic $topic: $e');
    }
  }

  // Record topic subscription in Firestore
  Future<void> _recordTopicSubscription(String topic, bool isSubscribed) async {
    try {
      User? currentUser = _auth.currentUser;
      if (currentUser == null) return;

      // Get the correct user document ID from UserDataService
      String? userDocId = await UserDataService().getUserDocumentId();
      if (userDocId == null) return;

      // Update the user's topic subscriptions
      await _firestore.collection('fcm_subscriptions').doc(userDocId).set({
        'userId': userDocId,
        'topics': {
          topic: {
            'subscribed': isSubscribed,
            'updatedAt': FieldValue.serverTimestamp(),
          }
        },
        'updatedAt': FieldValue.serverTimestamp(),
        'appType': 'ds_panel',
      }, SetOptions(merge: true));

      print('FCM: Topic subscription recorded in Firestore: $topic, subscribed: $isSubscribed');
    } catch (e) {
      print('FCM: Error recording topic subscription: $e');
    }
  }

  // Handle user login - register FCM token
  Future<void> handleUserLogin(User user) async {
    try {
      // Subscribe to user-specific topic
      await subscribeToTopic('user_${user.uid}');

      // Save FCM token to Firestore
      _fcmToken = await _firebaseMessaging.getToken();
      await _saveFCMTokenToFirestore();

      print('FCM: Handled user login for ${user.uid}');
    } catch (e) {
      print('FCM: Error handling user login: $e');
    }
  }

  // Handle user logout - clean up FCM token
  Future<void> handleUserLogout() async {
    try {
      User? user = _auth.currentUser;
      if (user != null) {
        // Unsubscribe from user-specific topic
        await unsubscribeFromTopic('user_${user.uid}');
      }

      print('FCM: Handled user logout');
    } catch (e) {
      print('FCM: Error handling user logout: $e');
    }
  }
}

// This handler must be a top-level function
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    // Ensure Firebase is initialized
    await Firebase.initializeApp();
    print('Handling a background message: ${message.messageId}');
    print('Message data: ${message.data}');

    if (message.notification != null) {
      print('Message notification: ${message.notification?.title}');

      // We can't directly use our services here because this is a separate isolate
      // The notification will be handled when the app is opened
      // But we can save it to Firestore for persistence

      try {
        // Get current user
        User? user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          // Extract notification data
          String title = message.notification?.title ?? 'New Notification';
          String body = message.notification?.body ?? '';
          String payload = message.data.toString();
          String? messageId = message.messageId;

          // Add a unique identifier to the payload to prevent duplicates
          String enhancedPayload = messageId != null ?
              '{"fcmMessageId":"$messageId", "data":$payload}' : payload;

          // Check if this notification has already been processed
          QuerySnapshot existingNotifications = await FirebaseFirestore.instance
              .collection('ds_notifications')
              .where('userId', isEqualTo: user.uid)
              .where('fcmMessageId', isEqualTo: messageId)
              .limit(1)
              .get();

          if (existingNotifications.docs.isEmpty) {
            // Save to Firestore with the message ID to prevent duplicates
            await FirebaseFirestore.instance.collection('ds_notifications').add({
              'userId': user.uid,
              'title': title,
              'body': body,
              'payload': enhancedPayload,
              'isRead': false,
              'createdAt': FieldValue.serverTimestamp(),
              'receivedInBackground': true,
              'fcmMessageId': messageId, // Store the message ID to prevent duplicates
            });
            print('Background notification saved to Firestore');
          } else {
            print('Duplicate notification detected and skipped: $messageId');
          }
        }
      } catch (e) {
        print('Error saving background notification: $e');
      }
    }
  } catch (e) {
    print('Error handling background message: $e');
  }
}


