import 'package:flutter/material.dart';
import 'app_localizations.dart';

// Extension method on BuildContext
extension LocalizationExtension on BuildContext {
  // Shorthand method to access translations
  String translate(String key) {
    return AppLocalizations.of(this).translate(key);
  }
}

// Extension method for any widget
extension LocalizationWidgetExtension on Widget {
  // Shorthand method to access translations from a widget
  String translateFromContext(BuildContext context, String key) {
    return AppLocalizations.of(context).translate(key);
  }
}
