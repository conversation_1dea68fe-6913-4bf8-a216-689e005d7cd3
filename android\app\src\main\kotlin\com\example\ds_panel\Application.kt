package com.example.ds_panel

import io.flutter.app.FlutterApplication
import com.google.firebase.FirebaseApp
import com.google.firebase.appcheck.FirebaseAppCheck
import com.google.firebase.appcheck.debug.DebugAppCheckProviderFactory
import com.google.firebase.appcheck.playintegrity.PlayIntegrityAppCheckProviderFactory
import android.util.Log
import com.google.firebase.appcheck.AppCheckToken
import com.google.firebase.appcheck.AppCheckTokenResult

class Application : FlutterApplication() {
    companion object {
        private const val TAG = "DSPanel"
    }

    override fun onCreate() {
        super.onCreate()
        initializeFirebase()
    }

    private fun initializeFirebase() {
        try {
            // Initialize Firebase first
            if (FirebaseApp.getApps(this).isEmpty()) {
                FirebaseApp.initializeApp(this)
                Log.d(TAG, "✅ Firebase core initialized successfully")
            }

            // Initialize App Check with fallback chain
            initializeAppCheck()
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize Firebase: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun initializeAppCheck() {
        val firebaseAppCheck = FirebaseAppCheck.getInstance()

        try {
            if (BuildConfig.DEBUG) {
                // Debug mode - use debug provider
                firebaseAppCheck.installAppCheckProviderFactory(
                    DebugAppCheckProviderFactory.getInstance()
                )
                Log.d(TAG, "✅ Debug App Check provider installed")
            } else {
                // Production mode - try Play Integrity first
                try {
                    firebaseAppCheck.installAppCheckProviderFactory(
                        PlayIntegrityAppCheckProviderFactory.getInstance()
                    )
                    Log.d(TAG, "✅ Play Integrity provider installed")
                } catch (e: Exception) {
                    Log.w(TAG, "⚠️ Play Integrity not available: ${e.message}")
                    
                    // Fallback to custom provider
                    firebaseAppCheck.installAppCheckProviderFactory(
                        CustomAppCheckProviderFactory.getInstance()
                    )
                    Log.d(TAG, "✅ Custom App Check provider installed as fallback")
                }
            }

            // Add token listener for debugging
            firebaseAppCheck.addAppCheckTokenListener { tokenResult ->
                when {
                    tokenResult.token != null -> {
                        Log.d(TAG, "✅ App Check token refreshed successfully")
                    }
                    tokenResult.error != null -> {
                        Log.e(TAG, "❌ App Check token refresh failed: ${tokenResult.error?.message}")
                        handleTokenError(tokenResult)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to initialize App Check: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun handleTokenError(tokenResult: AppCheckTokenResult) {
        val error = tokenResult.error ?: return
        val errorMessage = error.message ?: "Unknown error"
        
        when {
            errorMessage.contains("Play Integrity", ignoreCase = true) -> {
                Log.w(TAG, "🔄 Play Integrity failed, switching to custom provider")
                FirebaseAppCheck.getInstance().installAppCheckProviderFactory(
                    CustomAppCheckProviderFactory.getInstance()
                )
            }
            errorMessage.contains("network", ignoreCase = true) -> {
                Log.w(TAG, "🌐 Network error in App Check, will retry automatically")
            }
            else -> {
                Log.e(TAG, "⚠️ Unhandled App Check error: $errorMessage")
            }
        }
    }
}
