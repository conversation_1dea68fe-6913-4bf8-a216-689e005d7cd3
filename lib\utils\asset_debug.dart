import 'package:flutter/services.dart';

class AssetDebug {
  static Future<void> listAssets() async {
    try {
      print('Attempting to list assets...');
      
      // Try to load a specific asset to see if it exists
      try {
        final String enJson = await rootBundle.loadString('assets/lang/en.json');
        print('Successfully loaded en.json, length: ${enJson.length}');
      } catch (e) {
        print('Failed to load en.json: $e');
      }
      
      try {
        final String hiJson = await rootBundle.loadString('assets/lang/hi.json');
        print('Successfully loaded hi.json, length: ${hiJson.length}');
      } catch (e) {
        print('Failed to load hi.json: $e');
      }
      
      // Try to load a known asset to verify asset loading works
      try {
        final ByteData data = await rootBundle.load('FontManifest.json');
        print('Successfully loaded FontManifest.json, size: ${data.lengthInBytes} bytes');
      } catch (e) {
        print('Failed to load FontManifest.json: $e');
      }
      
      print('Asset debugging complete');
    } catch (e) {
      print('Error in asset debugging: $e');
    }
  }
}
