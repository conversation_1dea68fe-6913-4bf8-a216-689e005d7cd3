Write-Host "🔄 Fixing Android build configuration..." -ForegroundColor Cyan

# Set working directory
$projectRoot = $PSScriptRoot
$androidDir = Join-Path $projectRoot "android"
Set-Location $androidDir

# Clean Gradle cache
Write-Host "🧹 Cleaning Gradle cache..." -ForegroundColor Yellow
Remove-Item -Path "$env:USERPROFILE\.gradle\caches\*" -Recurse -Force -ErrorAction SilentlyContinue

# Update Gradle wrapper
Write-Host "⚙️ Updating Gradle wrapper..." -ForegroundColor Yellow
.\gradlew wrapper --gradle-version 8.3 --distribution-type all

# Clean and build
Write-Host "🔨 Cleaning project..." -ForegroundColor Yellow
.\gradlew clean

Write-Host "📦 Building project..." -ForegroundColor Yellow
.\gradlew assembleRelease --info

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Build failed with exit code $LASTEXITCODE" -ForegroundColor Red
}

Set-Location $projectRoot
