@echo off
echo Building APK with App Check implementation...

REM Clean the Flutter project
echo Cleaning Flutter project...
flutter clean

REM Clean Android build
echo Cleaning Android build...
cd android
call gradlew clean
cd ..

REM Get dependencies
echo Getting dependencies...
flutter pub get

REM Build release APK
echo Building release APK with App Check...
flutter build apk --release

REM Check if build was successful
if errorlevel 1 (
    echo Build failed! Check the error messages above.
    exit /b 1
) else (
    echo Build completed successfully!
    echo APK is available in build/app/outputs/flutter-apk/app-release.apk
)
