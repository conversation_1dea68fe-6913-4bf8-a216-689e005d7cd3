import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:http/http.dart' as http;
import 'package:firebase_app_check/firebase_app_check.dart';
import '../config/app_check_config.dart';
import '../utils/retry_util.dart';

/// Custom App Check Provider for direct APK distribution
/// This provider communicates with your backend to get App Check tokens
class CustomAppCheckProvider {
  static const String _backendUrl = AppCheckConfig.backendUrl;
  static const String _appSecret = AppCheckConfig.appSecret;
  static const String _appSignature = AppCheckConfig.appSignature;

  /// Generate token for App Check with retry logic
  static Future<String?> getToken() async {
    try {
      return await RetryUtil.withRetry(
        operation: () => _getTokenWithValidation(),
        maxAttempts: AppCheckConfig.maxRetryAttempts,
        initialDelay: AppCheckConfig.retryDelay,
        operationName: 'App Check token generation',
      );
    } catch (e) {
      print('❌ Error getting custom token after all retries: $e');
      return null;
    }
  }

  /// Get token with validation
  static Future<String> _getTokenWithValidation() async {
    final token = await requestAppCheckTokenFromBackend();
    if (token == null) {
      throw Exception('Failed to get token from backend');
    }
    
    final isValid = await validateTokenWithBackend(token);
    if (!isValid) {
      throw Exception('Token validation failed');
    }
    
    return token;
  }

  /// Request App Check token from backend with device verification
  static Future<String?> requestAppCheckTokenFromBackend() async {
    try {
      final credentials = await _generateCustomCredentials();
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      
      // Enhanced device fingerprint with more secure attributes
      final deviceFingerprint = {
        'manufacturer': deviceInfo.manufacturer,
        'model': deviceInfo.model,
        'androidId': deviceInfo.id,
        'fingerprint': deviceInfo.fingerprint,
        'securityPatch': deviceInfo.version.securityPatch,
        'sdkInt': deviceInfo.version.sdkInt,
        'bootTime': deviceInfo.bootloader,
        'brand': deviceInfo.brand,
        'hardware': deviceInfo.hardware,
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse(_backendUrl),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-Verification': base64Encode(utf8.encode(json.encode(deviceFingerprint))),
          'X-App-Secret': credentials['deviceSecret'],
        },
        body: json.encode({
          'deviceInfo': deviceFingerprint,
          'appSignature': credentials['appSignature'],
          'nonce': DateTime.now().millisecondsSinceEpoch.toString(),
          'timestamp': credentials['timestamp'],
        }),
      ).timeout(AppCheckConfig.tokenRequestTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final token = data['token'] as String?;
        
        if (token != null && token.isNotEmpty) {
          print('✅ Successfully received custom token from backend');
          return token;
        }
      }
      print('❌ Backend returned error: ${response.statusCode}');
      print('Error response: ${response.body}');
      return null;
    } catch (e) {
      print('❌ Network error while getting custom token: $e');
      return null;
    }
  }

  /// Generate custom credentials for backend validation
  static Future<Map<String, dynamic>> _generateCustomCredentials() async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final payload = '$_appSecret:$_appSignature:$deviceInfo:$timestamp';
      final deviceSecret = _generateSHA256(payload);

      return {
        'deviceSecret': deviceSecret,
        'appSignature': _appSignature,
        'timestamp': timestamp,
      };
    } catch (e) {
      print('❌ Error generating custom credentials: $e');
      rethrow;
    }
  }

  /// Get device information for credentials
  static Future<String> _getDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        return '${androidInfo.manufacturer}-${androidInfo.model}-${androidInfo.device}:${androidInfo.version.release}:${androidInfo.version.sdkInt}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        return '${iosInfo.name}-${iosInfo.model}-${iosInfo.systemName}:${iosInfo.systemVersion}';
      }
      return 'unknown-device';
    } catch (e) {
      print('❌ Error getting device info: $e');
      rethrow;
    }
  }

  /// Validate token with backend server
  static Future<bool> validateTokenWithBackend(String token) async {
    try {
      final deviceInfo = await DeviceInfoPlugin().androidInfo;
      final verificationData = {
        'token': token,
        'deviceInfo': {
          'manufacturer': deviceInfo.manufacturer,
          'model': deviceInfo.model,
          'androidId': deviceInfo.id,
          'fingerprint': deviceInfo.fingerprint,
          'timestamp': DateTime.now().toIso8601String(),
        },
      };

      final response = await http.post(
        Uri.parse(AppCheckConfig.validateTokenEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'X-App-Signature': _generateSHA256(_appSignature),
        },
        body: json.encode(verificationData),
      ).timeout(AppCheckConfig.tokenRequestTimeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['valid'] == true) {
          return true;
        }
        print('❌ Token validation failed: ${data['error']}');
        return false;
      }

      print('❌ Token validation failed with status ${response.statusCode}');
      return false;
    } catch (e) {
      print('❌ Token validation error: $e');
      return false;
    }
  }

  /// Generate SHA-256 hash with additional security measures
  static String _generateSHA256(String input) {
    try {
      final bytes = utf8.encode(input);
      final digest = sha256.convert(bytes);
      return digest.toString();
    } catch (e) {
      print('❌ Error generating hash: $e');
      throw Exception('Failed to generate secure hash');
    }
  }

  /// Verify the integrity of credentials
  static bool _verifyCredentials(Map<String, dynamic> credentials) {
    if (!credentials.containsKey('deviceSecret') || 
        !credentials.containsKey('appSignature') ||
        !credentials.containsKey('timestamp')) {
      return false;
    }

    final timestamp = credentials['timestamp'] as int;
    final now = DateTime.now().millisecondsSinceEpoch;
    final age = now - timestamp;

    // Check if credentials are too old
    if (age > AppCheckConfig.maxTokenAge.inMilliseconds) {
      print('⚠️ Credentials expired: ${age}ms old');
      return false;
    }

    return true;
  }
}
