/**
 * Backend Service for Custom App Check Token Generation
 * This service validates custom credentials and generates App Check tokens using Firebase Admin SDK
 */

const admin = require('firebase-admin');
const express = require('express');
const crypto = require('crypto');
const { getFirestore } = require('firebase-admin/firestore');

// Initialize Firebase Admin SDK
const serviceAccount = require('./path/to/your/service-account-key.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // Add your project config here
});

const db = getFirestore();

const app = express();
app.use(express.json());

// Configuration
const APP_ID = 'com.example.ds_panel'; // Your Android app ID
const APP_SECRET = 'quickk-ds-panel-custom-secret-2024'; // Your app secret
const TOKEN_TTL_SECONDS = 3600; // 1 hour

/**
 * Validate custom credentials from your app
 */
function validateCustomCredentials(credentials) {
    try {
        const { deviceSecret, appSignature, deviceInfo, timestamp } = credentials;
        
        // Validate timestamp (not too old)
        const now = Date.now();
        const credentialAge = now - timestamp;
        const maxAge = 5 * 60 * 1000; // 5 minutes
        
        if (credentialAge > maxAge) {
            return { valid: false, error: 'Credentials too old' };
        }
        
        // Validate device secret format
        if (!deviceSecret || deviceSecret.length < 32) {
            return { valid: false, error: 'Invalid device secret' };
        }
        
        // Validate app signature (you can store known signatures in database)
        if (!appSignature || appSignature.length < 32) {
            return { valid: false, error: 'Invalid app signature' };
        }
        
        // Generate expected hash
        const expectedPayload = `${APP_SECRET}:${appSignature}:${deviceInfo}:${timestamp}`;
        const expectedHash = crypto.createHash('sha256').update(expectedPayload).digest('hex');
        
        // Validate device secret matches expected hash
        if (deviceSecret !== expectedHash) {
            return { valid: false, error: 'Invalid credentials hash' };
        }
        
        console.log('Custom credentials validated successfully:', {
            deviceInfo: deviceInfo.substring(0, 20) + '...',
            timestamp: new Date(timestamp).toISOString(),
            age: `${Math.round(credentialAge / 1000)}s`
        });
        
        return { valid: true };
        
    } catch (error) {
        return { valid: false, error: error.message };
    }
}

/**
 * Generate App Check token using Firebase Admin SDK
 */
async function generateAppCheckToken(appId, customClaims = {}) {
    try {
        // Create App Check token with custom claims
        const appCheckToken = await admin.appCheck().createToken(appId, {
            ttlMillis: TOKEN_TTL_SECONDS * 1000,
            customClaims: customClaims
        });
        
        console.log('App Check token generated successfully:', {
            appId: appId,
            tokenLength: appCheckToken.token.length,
            ttlSeconds: TOKEN_TTL_SECONDS
        });
        
        return {
            success: true,
            token: appCheckToken.token,
            ttlMillis: appCheckToken.ttlMillis,
            customClaims: customClaims
        };
        
    } catch (error) {
        console.error('Error generating App Check token:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * API Endpoint: Generate App Check Token
 * POST /generate-appcheck-token
 */
app.post('/generate-appcheck-token', async (req, res) => {
    try {
        const { credentials } = req.body;
        
        if (!credentials) {
            return res.status(400).json({ error: 'Missing credentials' });
        }
        
        // Step 1: Validate custom credentials
        const validation = validateCustomCredentials(credentials);
        
        if (!validation.valid) {
            return res.status(401).json({ 
                error: 'Invalid credentials',
                details: validation.error
            });
        }
        
        // Step 2: Generate App Check token
        const customClaims = {
            deviceInfo: credentials.deviceInfo,
            timestamp: credentials.timestamp,
            source: 'custom-backend'
        };
        
        const tokenResult = await generateAppCheckToken(APP_ID, customClaims);
        
        if (!tokenResult.success) {
            return res.status(500).json({
                error: 'Failed to generate App Check token',
                details: tokenResult.error
            });
        }
        
        // Step 3: Return App Check token
        res.json({
            success: true,
            appCheckToken: tokenResult.token,
            ttlMillis: tokenResult.ttlMillis,
            expiresAt: new Date(Date.now() + tokenResult.ttlMillis).toISOString(),
            message: 'App Check token generated successfully'
        });
        
    } catch (error) {
        console.error('Error in generate-appcheck-token endpoint:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * API Endpoint: Validate App Check Token
 * POST /validate-appcheck-token
 */
app.post('/validate-appcheck-token', async (req, res) => {
    try {
        const { token } = req.body;
        
        if (!token) {
            return res.status(400).json({ error: 'Missing token' });
        }
        
        // Verify App Check token
        const decodedToken = await admin.appCheck().verifyToken(token);
        
        res.json({
            valid: true,
            appId: decodedToken.app_id,
            customClaims: decodedToken.custom_claims || {},
            message: 'App Check token is valid'
        });
        
    } catch (error) {
        console.error('Error validating App Check token:', error);
        res.status(401).json({
            valid: false,
            error: 'Invalid App Check token',
            details: error.message
        });
    }
});

/**
 * Health check endpoint
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'Custom App Check Token Service',
        timestamp: new Date().toISOString()
    });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Custom App Check Token Service running on port ${PORT}`);
    console.log('Endpoints:');
    console.log('  POST /generate-appcheck-token - Generate App Check tokens');
    console.log('  POST /validate-appcheck-token - Validate App Check tokens');
    console.log('  GET /health - Health check');
});

/**
 * Token generation and validation
 */
exports.generateAppCheckToken = functions.https.onCall(async (data, context) => {
  try {
    const { deviceInfo, nonce } = data;
    
    if (!deviceInfo || !nonce) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Validate device
    const isValid = await validateDevice(deviceInfo);
    if (!isValid) {
      throw new functions.https.HttpsError('permission-denied', 'Device validation failed');
    }

    // Generate device fingerprint
    const fingerprint = generateDeviceFingerprint(deviceInfo);
    
    // Check rate limiting
    await enforceRateLimiting(fingerprint);
    
    // Generate App Check token
    const token = await admin.appCheck().createToken(process.env.FIREBASE_APP_ID, {
      ttlMillis: 3600000, // 1 hour
    });

    // Store token metadata
    await storeTokenMetadata(fingerprint, token.token);

    return { token: token.token };
  } catch (error) {
    console.error('Error generating App Check token:', error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

async function validateDevice(deviceInfo) {
  const {
    manufacturer,
    model,
    androidId,
    fingerprint,
    securityPatch,
    sdkInt
  } = deviceInfo;

  // Basic validation
  if (!manufacturer || !model || !androidId || !fingerprint) {
    return false;
  }

  // Minimum SDK requirement (Android 6.0)
  if (sdkInt < 23) {
    return false;
  }

  // Get device record from Firestore
  const deviceDoc = await db.collection('verified_devices')
    .doc(androidId)
    .get();

  if (!deviceDoc.exists) {
    // First time device, store it
    await db.collection('verified_devices').doc(androidId).set({
      manufacturer,
      model,
      fingerprint,
      securityPatch,
      sdkInt,
      firstSeen: admin.firestore.FieldValue.serverTimestamp(),
      lastSeen: admin.firestore.FieldValue.serverTimestamp(),
      tokenCount: 0
    });
    return true;
  }

  // Update existing device record
  await deviceDoc.ref.update({
    lastSeen: admin.firestore.FieldValue.serverTimestamp(),
    tokenCount: admin.firestore.FieldValue.increment(1)
  });

  return true;
}

function generateDeviceFingerprint(deviceInfo) {
  return crypto
    .createHash('sha256')
    .update(JSON.stringify(deviceInfo))
    .digest('hex');
}

async function enforceRateLimiting(fingerprint) {
  const rateLimitDoc = await db.collection('rate_limits')
    .doc(fingerprint)
    .get();

  if (rateLimitDoc.exists) {
    const { count, lastRequest } = rateLimitDoc.data();
    const oneHourAgo = Date.now() - 3600000;

    if (lastRequest > oneHourAgo && count >= 10) {
      throw new Error('Rate limit exceeded');
    }

    await rateLimitDoc.ref.update({
      count: lastRequest > oneHourAgo ? count + 1 : 1,
      lastRequest: Date.now()
    });
  } else {
    await rateLimitDoc.ref.set({
      count: 1,
      lastRequest: Date.now()
    });
  }
}

async function storeTokenMetadata(fingerprint, token) {
  const tokenHash = crypto
    .createHash('sha256')
    .update(token)
    .digest('hex');

  await db.collection('token_metadata')
    .doc(tokenHash)
    .set({
      fingerprint,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: admin.firestore.Timestamp.fromMillis(Date.now() + 3600000)
    });
}

// Token validation endpoint
exports.validateAppCheckToken = functions.https.onCall(async (data, context) => {
  try {
    const { token, deviceInfo } = data;
    
    if (!token || !deviceInfo) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing token or device info');
    }

    const tokenHash = crypto
      .createHash('sha256')
      .update(token)
      .digest('hex');

    const tokenDoc = await db.collection('token_metadata')
      .doc(tokenHash)
      .get();

    if (!tokenDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Token not found');
    }

    const tokenData = tokenDoc.data();
    
    // Check if token is expired
    if (tokenData.expiresAt.toMillis() < Date.now()) {
      throw new functions.https.HttpsError('failed-precondition', 'Token expired');
    }

    // Verify device fingerprint matches
    const currentFingerprint = generateDeviceFingerprint(deviceInfo);
    if (currentFingerprint !== tokenData.fingerprint) {
      throw new functions.https.HttpsError('permission-denied', 'Device mismatch');
    }

    return { valid: true };
  } catch (error) {
    console.error('Error validating App Check token:', error);
    throw new functions.https.HttpsError('internal', error.message);
  }
});

/**
 * Deployment Instructions:
 * 
 * 1. Install dependencies:
 *    npm install firebase-admin express
 * 
 * 2. Download service account key:
 *    - Go to Firebase Console > Project Settings > Service Accounts
 *    - Generate new private key
 *    - Save as service-account-key.json
 * 
 * 3. Update configuration:
 *    - Set your APP_ID (com.example.ds_panel)
 *    - Set your APP_SECRET (match with app)
 *    - Update service account path
 * 
 * 4. Deploy to your server:
 *    - Heroku: git push heroku main
 *    - Google Cloud Run: gcloud run deploy
 *    - Firebase Functions: firebase deploy --only functions
 *    - Any Node.js hosting service
 * 
 * 5. Update your app to call this service
 */
