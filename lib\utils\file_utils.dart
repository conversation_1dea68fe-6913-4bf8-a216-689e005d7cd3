import 'dart:io';
import 'package:flutter/services.dart';

class FileUtils {
  // Simple implementation that doesn't rely on path_provider
  static Future<String> getLocalPath() async {
    try {
      // Use a hardcoded path for simplicity
      return Directory.systemTemp.path;
    } catch (e) {
      print('Error getting local path: $e');
      return '.';
    }
  }

  static Future<File> getLocalFile(String filename) async {
    final path = await getLocalPath();
    return File('$path/$filename');
  }

  static Future<bool> copyAssetToLocal(String assetPath, String localFilename) async {
    try {
      print('Copying asset $assetPath to local file $localFilename');
      
      // Read asset file
      final data = await rootBundle.load(assetPath);
      final bytes = data.buffer.asUint8List();
      
      // Write to local file
      final file = await getLocalFile(localFilename);
      await file.writeAsBytes(bytes);
      
      print('Successfully copied asset to ${file.path}');
      return true;
    } catch (e) {
      print('Error copying asset to local file: $e');
      return false;
    }
  }

  static Future<String?> readLocalFile(String filename) async {
    try {
      final file = await getLocalFile(filename);
      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      print('Error reading local file: $e');
      return null;
    }
  }

  static Future<bool> writeLocalFile(String filename, String content) async {
    try {
      final file = await getLocalFile(filename);
      await file.writeAsString(content);
      return true;
    } catch (e) {
      print('Error writing local file: $e');
      return false;
    }
  }

  static Future<bool> copyLanguageFilesToLocal() async {
    try {
      bool success = true;
      
      // Copy English language file
      if (!await copyAssetToLocal('assets/lang/en.json', 'en.json')) {
        success = false;
      }
      
      // Copy Hindi language file
      if (!await copyAssetToLocal('assets/lang/hi.json', 'hi.json')) {
        success = false;
      }
      
      return success;
    } catch (e) {
      print('Error copying language files: $e');
      return false;
    }
  }
}
