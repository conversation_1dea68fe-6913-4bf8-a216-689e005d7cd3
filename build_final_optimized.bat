@echo off
echo Building final optimized APK...

REM Clean the project
flutter clean

REM Get dependencies
flutter pub get

REM Build APK with optimized main file
echo Building optimized APK...
flutter build apk --release --target-platform android-arm64 --target lib/main_optimized.dart --obfuscate --split-debug-info=./debug-info

echo Build completed!
echo APK is available in build\app\outputs\apk\release\quickk-ds-panel.apk
