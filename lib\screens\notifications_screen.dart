import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import '../theme/app_theme.dart';
import '../services/notification_service.dart';
import '../services/user_data_service.dart';
import '../utils/localization.dart';

class NotificationsScreen extends StatefulWidget {
  @override
  _NotificationsScreenState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final NotificationService _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    // Mark all notifications as read when the screen is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notificationService.markAllNotificationsAsRead();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.translate('notifications')),
        actions: [
          IconButton(
            icon: Icon(Icons.delete_sweep),
            tooltip: context.translate('clean_up_notifications'),
            onPressed: () {
              _showCleanupConfirmationDialog();
            },
          ),
        ],
      ),
      body: _buildNotificationsList(),
    );
  }

  Widget _buildNotificationsList() {
    return FutureBuilder<String?>(
      future: UserDataService().getUserDocumentId(),
      builder: (context, userDocSnapshot) {
        if (userDocSnapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        String? userDocId = userDocSnapshot.data;
        if (userDocId == null) {
          return Center(child: Text('Please log in to view notifications'));
        }

        return StreamBuilder<QuerySnapshot>(
          stream: FirebaseFirestore.instance
              .collection('ds_notifications')
              .where('userId', isEqualTo: userDocId)
              // Temporarily remove ordering until the index is created
              // .orderBy('createdAt', descending: true)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }

            if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.notifications_off,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      context.translate('no_notifications'),
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              );
            }

            // Sort notifications by createdAt timestamp (newest first)
            final docs = snapshot.data!.docs.toList();
            docs.sort((a, b) {
              final aTimestamp = a['createdAt'] as Timestamp;
              final bTimestamp = b['createdAt'] as Timestamp;
              return bTimestamp.compareTo(aTimestamp); // Descending order
            });

            return ListView.builder(
              itemCount: docs.length,
              itemBuilder: (context, index) {
                var notification = docs[index];
                var data = notification.data() as Map<String, dynamic>;
                bool isRead = data['isRead'] ?? false;
                Timestamp timestamp = data['createdAt'] as Timestamp;
                DateTime dateTime = timestamp.toDate();
                String formattedDate = DateFormat('MMM d, yyyy • h:mm a').format(dateTime);

                return Card(
                  margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: isRead ? Colors.transparent : AppTheme.primaryColor.withOpacity(0.3),
                      width: isRead ? 0 : 1,
                    ),
                  ),
                  elevation: isRead ? 1 : 2,
                  child: ListTile(
                    contentPadding: EdgeInsets.all(16),
                    title: Row(
                      children: [
                        Expanded(
                          child: Text(
                            data['title'] ?? 'Notification',
                            style: TextStyle(
                              fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        if (!isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 8),
                        Text(
                          data['body'] ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          formattedDate,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      // Mark as read if not already read
                      if (!isRead) {
                        _notificationService.markNotificationAsRead(notification.id);
                      }

                      // Show notification details popup
                      _showNotificationDetailsDialog(
                        title: data['title'] ?? 'Notification',
                        body: data['body'] ?? '',
                        timestamp: formattedDate,
                        isRead: isRead,
                      );
                    },
                  ),
                );
              },
            );
          },
        );
      },
    );
  }



  // Show notification details in a popup dialog
  void _showNotificationDetailsDialog({
    required String title,
    required String body,
    required String timestamp,
    required bool isRead,
  }) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 8,
        child: Container(
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status indicator
              Row(
                children: [
                  // Icon
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.notifications,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 12),
                  // Title
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                  // Status indicator
                  if (!isRead)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        context.translate('new'),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                ],
              ),

              Divider(height: 24),

              // Timestamp
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                  SizedBox(width: 6),
                  Text(
                    timestamp,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 16),

              // Message body
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.4,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.1),
                  ),
                ),
                padding: EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: Text(
                    body,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textPrimaryColor,
                      height: 1.4,
                    ),
                  ),
                ),
              ),

              SizedBox(height: 20),

              // Close button
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  ),
                  child: Text(context.translate('close')),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCleanupConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.translate('clean_up_notifications')),
        content: Text(context.translate('clean_up_notifications_confirm')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.translate('cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _notificationService.cleanupOldNotifications();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(context.translate('old_notifications_cleaned_up'))),
              );
            },
            child: Text(context.translate('clean_up')),
          ),
        ],
      ),
    );
  }
}
