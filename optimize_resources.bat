@echo off
echo Optimizing resources to reduce APK size...

REM Create optimized directory structure
if not exist "optimized_assets" mkdir optimized_assets
if not exist "optimized_assets\images" mkdir optimized_assets\images
if not exist "optimized_assets\lang" mkdir optimized_assets\lang

REM Copy only necessary language files
echo Copying optimized language files...
copy assets\lang\en.json optimized_assets\lang\
copy assets\lang\hi.json optimized_assets\lang\

REM Optimize images (this is a placeholder - in a real scenario you would use tools like pngquant)
echo Optimizing images...
xcopy assets\images\*.* optimized_assets\images\ /Y

echo Resource optimization completed!
echo You can now build the app with optimized resources.
