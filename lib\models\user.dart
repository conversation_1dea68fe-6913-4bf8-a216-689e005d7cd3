class User {
  final String name;
  final String dsId;
  final String mobileNumber;
  final String kyc;

  User({required this.name, required this.dsId, required this.mobileNumber, required this.kyc});

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'dsId': dsId,
      'mobileNumber': mobileNumber,
      'kyc': kyc,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      name: map['name'],
      dsId: map['dsId'],
      mobileNumber: map['mobileNumber'],
      kyc: map['kyc'],
    );
  }
}
