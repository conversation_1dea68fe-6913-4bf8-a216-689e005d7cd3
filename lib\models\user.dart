class User {
  final String name;
  final String dsId;
  final String mobileNumber;
  final String kyc;
  final String? email;
  final String? password;

  User({required this.name, required this.dsId, required this.mobileNumber, required this.kyc, this.email, this.password});

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'dsId': dsId,
      'mobileNumber': mobileNumber,
      'kyc': kyc,
      if (email != null) 'email': email,
      if (password != null) 'password': password,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      name: map['name'],
      dsId: map['dsId'],
      mobileNumber: map['mobileNumber'],
      kyc: map['kyc'],
      email: map['email'],
      password: map['password'],
    );
  }
}
