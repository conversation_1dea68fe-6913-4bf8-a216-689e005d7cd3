class User {
  final String name;
  final String dsId;
  final String mobileNumber;
  final String kyc;
  final String? email;

  User({required this.name, required this.dsId, required this.mobileNumber, required this.kyc, this.email});

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'dsId': dsId,
      'mobileNumber': mobileNumber,
      'kyc': kyc,
      if (email != null) 'email': email,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      name: map['name'],
      dsId: map['dsId'],
      mobileNumber: map['mobileNumber'],
      kyc: map['kyc'],
      email: map['email'],
    );
  }
}
